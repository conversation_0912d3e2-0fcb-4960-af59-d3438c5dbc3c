<component name="libraryTable">
  <library name="Dart Packages" type="DartPackagesLibraryType">
    <properties>
      <option name="packageNameToDirsMap">
        <entry key="_fe_analyzer_shared">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/_fe_analyzer_shared-83.0.0/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/_fe_analyzer_shared-80.0.0/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/_fe_analyzer_shared-76.0.0/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/_fe_analyzer_shared-82.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="_flutterfire_internals">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/_flutterfire_internals-1.3.56/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/_flutterfire_internals-1.3.55/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/_flutterfire_internals-1.3.16/lib" />
            </list>
          </value>
        </entry>
        <entry key="_macros">
          <value>
            <list>
              <option value="$USER_HOME$/Development/flutter/bin/cache/dart-sdk/pkg/_macros/lib" />
            </list>
          </value>
        </entry>
        <entry key="analyzer">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/analyzer-7.4.6/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/analyzer-7.3.0/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/analyzer-6.11.0/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/analyzer-7.4.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="another_xlider">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/another_xlider-3.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="ansi_styles">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/ansi_styles-0.3.2+1/lib" />
            </list>
          </value>
        </entry>
        <entry key="archive">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/archive-3.6.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="args">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/args-2.7.0/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/args-2.5.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="async">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/async-2.12.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="auto_route">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/auto_route-10.1.0+1/lib" />
            </list>
          </value>
        </entry>
        <entry key="auto_route_generator">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/auto_route_generator-10.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="awesome_dialog">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/awesome_dialog-3.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="bloc">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/bloc-8.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="bloc_test">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/bloc_test-9.1.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="boolean_selector">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="build">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/build-2.5.4/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/build-2.4.2/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/build-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="build_config">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/build_config-1.1.2/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/build_config-1.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="build_daemon">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/build_daemon-4.0.4/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/build_daemon-4.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="build_resolvers">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/build_resolvers-2.5.4/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/build_resolvers-2.4.4/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/build_resolvers-2.4.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="build_runner">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/build_runner-2.5.4/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/build_runner-2.4.15/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/build_runner-2.4.12/lib" />
            </list>
          </value>
        </entry>
        <entry key="build_runner_core">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/build_runner_core-9.1.2/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/build_runner_core-8.0.0/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/build_runner_core-7.3.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="built_collection">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/built_collection-5.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="built_value">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/built_value-8.10.1/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/built_value-8.9.5/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/built_value-8.9.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="cached_network_image">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="cached_network_image_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="cached_network_image_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/cached_network_image_web-1.3.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="calendar_date_picker2">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/calendar_date_picker2-1.1.9/lib" />
            </list>
          </value>
        </entry>
        <entry key="carousel_slider_x">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/carousel_slider_x-6.0.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="change_app_package_name">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/change_app_package_name-1.5.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="characters">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/characters-1.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="charcode">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/charcode-1.4.0/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/charcode-1.3.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="checked_yaml">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/checked_yaml-2.0.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="chewie">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/chewie-1.11.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="cli_config">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/cli_config-0.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="cli_launcher">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/cli_launcher-0.3.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="cli_util">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/cli_util-0.4.2/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/cli_util-0.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="clock">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/clock-1.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="cloud_firestore">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/cloud_firestore-5.6.8/lib" />
            </list>
          </value>
        </entry>
        <entry key="cloud_firestore_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.9/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.8/lib" />
            </list>
          </value>
        </entry>
        <entry key="cloud_firestore_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/cloud_firestore_web-4.4.9/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/cloud_firestore_web-4.4.8/lib" />
            </list>
          </value>
        </entry>
        <entry key="cloud_functions">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/cloud_functions-5.5.2/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/cloud_functions-5.5.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="cloud_functions_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/cloud_functions_platform_interface-5.8.3/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/cloud_functions_platform_interface-5.7.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="cloud_functions_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/cloud_functions_web-4.11.2/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/cloud_functions_web-4.11.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="code_builder">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/code_builder-4.10.1/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/code_builder-4.10.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="collection">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/collection-1.19.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="connectivity_plus">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="connectivity_plus_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="conventional_commit">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/conventional_commit-0.6.0+1/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/conventional_commit-0.6.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="convert">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/convert-3.1.2/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/convert-3.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="coverage">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/coverage-1.14.1/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/coverage-1.11.1/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/coverage-1.13.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="cross_file">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/lib" />
            </list>
          </value>
        </entry>
        <entry key="crypto">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/crypto-3.0.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="csslib">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="cupertino_icons">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/lib" />
            </list>
          </value>
        </entry>
        <entry key="dart_style">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/dart_style-3.1.0/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/dart_style-3.0.1/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/dart_style-2.3.6/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/dart_style-2.3.8/lib" />
            </list>
          </value>
        </entry>
        <entry key="date_picker_plus">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/date_picker_plus-4.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="dbus">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/dbus-0.7.10/lib" />
            </list>
          </value>
        </entry>
        <entry key="device_info_plus">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/device_info_plus-11.5.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="device_info_plus_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="diff_match_patch">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/diff_match_patch-0.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="dispose_scope">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/dispose_scope-2.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="equatable">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/equatable-2.0.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="fake_async">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/fake_async-1.3.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="ffi">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/ffi-2.1.0/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/ffi-2.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="file">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/file-7.0.1/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/file-6.1.4/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/file-7.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="file_selector_linux">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/lib" />
            </list>
          </value>
        </entry>
        <entry key="file_selector_macos">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+2/lib" />
            </list>
          </value>
        </entry>
        <entry key="file_selector_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="file_selector_windows">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/lib" />
            </list>
          </value>
        </entry>
        <entry key="firebase_app_check">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_app_check-0.3.2+7/lib" />
            </list>
          </value>
        </entry>
        <entry key="firebase_app_check_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_app_check_platform_interface-0.1.1+7/lib" />
            </list>
          </value>
        </entry>
        <entry key="firebase_app_check_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_app_check_web-0.2.0+11/lib" />
            </list>
          </value>
        </entry>
        <entry key="firebase_auth">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_auth-5.5.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="firebase_auth_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.0/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.6.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="firebase_auth_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_auth_web-5.15.0/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_auth_web-5.14.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="firebase_core">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_core-3.13.1/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_core-2.24.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="firebase_core_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.0/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="firebase_core_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_core_web-2.23.0/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_core_web-2.10.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="firebase_messaging">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_messaging-15.2.7/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_messaging-14.7.10/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_messaging-15.2.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="firebase_messaging_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_messaging_platform_interface-4.6.7/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_messaging_platform_interface-4.5.18/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_messaging_platform_interface-4.6.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="firebase_messaging_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_messaging_web-3.10.7/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_messaging_web-3.5.18/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_messaging_web-3.10.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="firebase_storage">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_storage-12.4.7/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_storage-12.4.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="firebase_storage_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_storage_platform_interface-5.2.7/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_storage_platform_interface-5.2.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="firebase_storage_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_storage_web-3.10.14/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_storage_web-3.10.13/lib" />
            </list>
          </value>
        </entry>
        <entry key="fixnum">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/fixnum-1.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="fl_chart">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/fl_chart-0.68.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter">
          <value>
            <list>
              <option value="$USER_HOME$/Development/flutter/packages/flutter/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_bloc">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_bloc-8.1.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_blue_plus">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_blue_plus-1.32.11/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_cache_manager">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_driver">
          <value>
            <list>
              <option value="$USER_HOME$/Development/flutter/packages/flutter_driver/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_hooks">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_launcher_icons">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_launcher_icons-0.14.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_lints">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_lints-4.0.0/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_lints-2.0.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_local_notifications">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_local_notifications-19.3.0/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_local_notifications-19.0.0/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_local_notifications-19.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_local_notifications_linux">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-6.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_local_notifications_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_local_notifications_platform_interface-9.1.0/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_local_notifications_platform_interface-9.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_local_notifications_windows">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_local_notifications_windows-1.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_plugin_android_lifecycle">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_screenutil">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_screenutil-5.9.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_slidable">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_slidable-4.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_svg">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_svg-2.2.0/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_svg-2.0.10+1/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_svg-2.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_test">
          <value>
            <list>
              <option value="$USER_HOME$/Development/flutter/packages/flutter_test/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_web_plugins">
          <value>
            <list>
              <option value="$USER_HOME$/Development/flutter/packages/flutter_web_plugins/lib" />
            </list>
          </value>
        </entry>
        <entry key="fluttertoast">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/lib" />
            </list>
          </value>
        </entry>
        <entry key="font_awesome_flutter">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/font_awesome_flutter-10.8.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="fpdart">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/fpdart-2.0.0-dev.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="freezed">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/freezed-2.5.8/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/freezed-2.5.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="freezed_annotation">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/freezed_annotation-2.4.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="frontend_server_client">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/frontend_server_client-4.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="fuchsia_remote_debug_protocol">
          <value>
            <list>
              <option value="$USER_HOME$/Development/flutter/packages/fuchsia_remote_debug_protocol/lib" />
            </list>
          </value>
        </entry>
        <entry key="get_it">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/get_it-7.7.0/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/get_it-8.0.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="glob">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/glob-2.1.3/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/glob-2.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="google_fonts">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/google_fonts-6.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="google_identity_services_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/google_identity_services_web-0.3.3+1/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/google_identity_services_web-0.3.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="google_sign_in">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/google_sign_in-6.2.2/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/google_sign_in-6.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="google_sign_in_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/google_sign_in_android-6.2.1/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/google_sign_in_android-6.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="google_sign_in_ios">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/google_sign_in_ios-5.7.8/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/google_sign_in_ios-5.8.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="google_sign_in_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/google_sign_in_platform_interface-2.5.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="google_sign_in_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/google_sign_in_web-0.12.4+4/lib" />
            </list>
          </value>
        </entry>
        <entry key="graphs">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/graphs-2.3.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="hive">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/hive-2.2.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="hive_ce">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/hive_ce-2.11.3/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/hive_ce-2.11.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="hive_ce_flutter">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/hive_ce_flutter-2.3.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="hive_generator">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/hive_generator-2.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="html">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/html-0.15.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="http">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/http-1.4.0/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/http-1.3.0/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/http-1.2.0/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/http-1.2.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="http_multi_server">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/http_multi_server-3.2.2/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/http_multi_server-3.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="http_parser">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/http_parser-4.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="image">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/image-4.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/image_picker-1.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_for_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_ios">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_linux">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_macos">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_windows">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/lib" />
            </list>
          </value>
        </entry>
        <entry key="injectable">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/injectable-2.5.0/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/injectable-2.4.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="injectable_generator">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/injectable_generator-2.7.0/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/injectable_generator-2.6.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="injector">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/injector-4.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="integration_test">
          <value>
            <list>
              <option value="$USER_HOME$/Development/flutter/packages/integration_test/lib" />
            </list>
          </value>
        </entry>
        <entry key="internet_connection_checker_plus">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/internet_connection_checker_plus-2.7.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="intl">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/intl-0.19.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="io">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/io-1.0.5/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/io-1.0.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="isolate_channel">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/isolate_channel-0.2.2+1/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/isolate_channel-0.2.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="js">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/js-0.7.2/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/js-0.6.7/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/js-0.7.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="json_annotation">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="json_serializable">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/json_serializable-6.9.4/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/json_serializable-6.8.0/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/json_serializable-6.9.5/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/json_serializable-6.9.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="leak_tracker">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/leak_tracker-10.0.8/lib" />
            </list>
          </value>
        </entry>
        <entry key="leak_tracker_flutter_testing">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/lib" />
            </list>
          </value>
        </entry>
        <entry key="leak_tracker_testing">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="lints">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/lints-4.0.0/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/lints-2.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="logging">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/logging-1.3.0/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/logging-1.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="lottie">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/lottie-3.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="macros">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/macros-0.1.3-main.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="matcher">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/matcher-0.12.17/lib" />
            </list>
          </value>
        </entry>
        <entry key="material_color_utilities">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="melos">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/melos-3.4.0/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/melos-6.1.0/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/melos-6.3.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="meta">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/meta-1.16.0/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/meta-1.17.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="mime">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/mime-2.0.0/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/mime-1.0.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="mockito">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/mockito-5.4.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="mocktail">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/mocktail-1.0.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="mustache_template">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/mustache_template-2.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="nested">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/nested-1.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="nm">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/nm-0.5.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="node_preamble">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/node_preamble-2.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="octo_image">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="os_detect">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/os_detect-2.0.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="package_config">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/package_config-2.2.0/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/package_config-2.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="package_info_plus">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="package_info_plus_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/package_info_plus_platform_interface-3.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="path">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/path-1.9.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_parsing">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/path_parsing-1.1.0/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/path_parsing-1.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider-2.1.5/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider-2.1.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_android-2.2.16/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_android-2.2.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_foundation">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_foundation-2.3.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_linux">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_windows">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_windows-2.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="patrol">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/patrol-3.14.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="patrol_finders">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/patrol_finders-2.7.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="patrol_log">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/patrol_log-0.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="petitparser">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="platform">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/platform-3.1.6/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/platform-3.1.4/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/platform-3.1.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="plugin_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib" />
            </list>
          </value>
        </entry>
        <entry key="pool">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/pool-1.5.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="process">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/process-5.0.3/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/process-4.2.4/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/process-5.0.2/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/process-5.0.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="prompts">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/prompts-2.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="provider">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/provider-6.1.5/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/provider-6.1.4/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/provider-6.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="pub_semver">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/pub_semver-2.2.0/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/pub_semver-2.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="pub_updater">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/pub_updater-0.3.1/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/pub_updater-0.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="pubspec">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/pubspec-2.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="pubspec_parse">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/pubspec_parse-1.5.0/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/pubspec_parse-1.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="quiver">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/quiver-3.2.2/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/quiver-3.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="recase">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/recase-4.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="rive">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/rive-0.13.20/lib" />
            </list>
          </value>
        </entry>
        <entry key="rive_common">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/rive_common-0.4.15/lib" />
            </list>
          </value>
        </entry>
        <entry key="rxdart">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/rxdart-0.27.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="share_plus">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/share_plus-10.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="share_plus_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/share_plus_platform_interface-5.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences-2.3.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.8/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_android-2.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_foundation">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_linux">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_windows">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="shelf">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/shelf-1.4.2/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/shelf-1.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="shelf_packages_handler">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/shelf_packages_handler-3.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="shelf_static">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/shelf_static-1.1.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="shelf_web_socket">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/shelf_web_socket-3.0.0/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/shelf_web_socket-2.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="showcaseview">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/showcaseview-3.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="sign_in_with_apple">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/sign_in_with_apple-6.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="sign_in_with_apple_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/sign_in_with_apple_platform_interface-1.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="sign_in_with_apple_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/sign_in_with_apple_web-2.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="simple_gesture_detector">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/simple_gesture_detector-0.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="skeletonizer">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/skeletonizer-1.4.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="sky_engine">
          <value>
            <list>
              <option value="$USER_HOME$/Development/flutter/bin/cache/pkg/sky_engine/lib" />
            </list>
          </value>
        </entry>
        <entry key="smooth_page_indicator">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="source_gen">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/source_gen-2.0.0/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/source_gen-1.5.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="source_helper">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/source_helper-1.3.5/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/source_helper-1.3.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="source_map_stack_trace">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/source_map_stack_trace-2.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="source_maps">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/source_maps-0.10.13/lib" />
            </list>
          </value>
        </entry>
        <entry key="source_span">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="sprintf">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="sqflite">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="sqflite_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="sqflite_common">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="sqflite_darwin">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="sqflite_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="stack_trace">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="stream_channel">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="stream_transform">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/stream_transform-2.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="string_scanner">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="sync_http">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/sync_http-0.3.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="syncfusion_flutter_core">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-25.2.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="syncfusion_flutter_gauges">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/syncfusion_flutter_gauges-25.2.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="synchronized">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/synchronized-3.3.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="table_calendar">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/table_calendar-3.1.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="term_glyph">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="test">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/test-1.25.15/lib" />
            </list>
          </value>
        </entry>
        <entry key="test_api">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/test_api-0.7.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="test_core">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/test_core-0.6.8/lib" />
            </list>
          </value>
        </entry>
        <entry key="timeago">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="timeline_tile">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/timeline_tile-2.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="timezone">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/timezone-0.10.1/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/timezone-0.10.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="timing">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/timing-1.0.2/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/timing-1.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="typed_data">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/typed_data-1.3.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="upgrader">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/upgrader-11.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="uri">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/uri-1.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_ios">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_linux">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_macos">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_windows">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="uuid">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="vector_graphics">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/vector_graphics-1.1.19/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/vector_graphics-1.1.11+1/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/vector_graphics-1.1.18/lib" />
            </list>
          </value>
        </entry>
        <entry key="vector_graphics_codec">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/vector_graphics_codec-1.1.13/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/vector_graphics_codec-1.1.11+1/lib" />
            </list>
          </value>
        </entry>
        <entry key="vector_graphics_compiler">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.17/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.11+1/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib" />
            </list>
          </value>
        </entry>
        <entry key="vector_math">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="version">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/version-3.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="video_player">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/video_player-2.10.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="video_player_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/video_player_android-2.8.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="video_player_avfoundation">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/video_player_avfoundation-2.7.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="video_player_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/video_player_platform_interface-6.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="video_player_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/video_player_web-2.3.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="vm_service">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/vm_service-14.3.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="wakelock_plus">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/wakelock_plus-1.3.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="wakelock_plus_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/wakelock_plus_platform_interface-1.2.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="watcher">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/watcher-1.1.0/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/watcher-1.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/web-1.1.1/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/web-0.3.0/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/web-1.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="web_socket">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/web_socket-1.0.1/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/web_socket-0.1.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="web_socket_channel">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.3/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.2/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="webdriver">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/webdriver-3.0.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="webkit_inspection_protocol">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/webkit_inspection_protocol-1.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="win32">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/win32-5.13.0/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/win32-5.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="win32_registry">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/win32_registry-2.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="xdg_directories">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/xdg_directories-1.0.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="xml">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/xml-6.5.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="yaml">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/yaml-3.1.3/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/yaml-3.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="yaml_edit">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/yaml_edit-2.2.2/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/yaml_edit-2.2.1/lib" />
            </list>
          </value>
        </entry>
      </option>
    </properties>
    <CLASSES>
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/_fe_analyzer_shared-76.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/_fe_analyzer_shared-80.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/_fe_analyzer_shared-82.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/_fe_analyzer_shared-83.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/_flutterfire_internals-1.3.16/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/_flutterfire_internals-1.3.55/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/_flutterfire_internals-1.3.56/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/analyzer-6.11.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/analyzer-7.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/analyzer-7.4.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/analyzer-7.4.6/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/another_xlider-3.0.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/ansi_styles-0.3.2+1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/archive-3.6.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/args-2.5.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/args-2.7.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/async-2.12.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/auto_route-10.1.0+1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/auto_route_generator-10.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/awesome_dialog-3.2.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/bloc-8.1.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/bloc_test-9.1.7/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/build-2.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/build-2.4.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/build-2.5.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/build_config-1.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/build_config-1.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/build_daemon-4.0.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/build_daemon-4.0.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/build_resolvers-2.4.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/build_resolvers-2.4.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/build_resolvers-2.5.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/build_runner-2.4.12/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/build_runner-2.4.15/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/build_runner-2.5.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/build_runner_core-7.3.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/build_runner_core-8.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/build_runner_core-9.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/built_collection-5.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/built_value-8.10.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/built_value-8.9.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/built_value-8.9.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/cached_network_image_web-1.3.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/calendar_date_picker2-1.1.9/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/carousel_slider_x-6.0.7/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/change_app_package_name-1.5.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/characters-1.4.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/charcode-1.3.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/charcode-1.4.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/checked_yaml-2.0.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/chewie-1.11.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/cli_config-0.2.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/cli_launcher-0.3.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/cli_util-0.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/cli_util-0.4.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/clock-1.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/cloud_firestore-5.6.8/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.8/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.9/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/cloud_firestore_web-4.4.8/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/cloud_firestore_web-4.4.9/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/cloud_functions-5.5.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/cloud_functions-5.5.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/cloud_functions_platform_interface-5.7.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/cloud_functions_platform_interface-5.8.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/cloud_functions_web-4.11.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/cloud_functions_web-4.11.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/code_builder-4.10.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/code_builder-4.10.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/collection-1.19.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/conventional_commit-0.6.0+1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/conventional_commit-0.6.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/convert-3.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/convert-3.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/coverage-1.11.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/coverage-1.13.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/coverage-1.14.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/crypto-3.0.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/dart_style-2.3.6/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/dart_style-2.3.8/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/dart_style-3.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/dart_style-3.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/date_picker_plus-4.2.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/dbus-0.7.10/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/device_info_plus-11.5.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/diff_match_patch-0.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/dispose_scope-2.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/equatable-2.0.7/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/fake_async-1.3.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/ffi-2.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/ffi-2.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/file-6.1.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/file-7.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/file-7.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_app_check-0.3.2+7/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_app_check_platform_interface-0.1.1+7/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_app_check_web-0.2.0+11/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_auth-5.5.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.6.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_auth_web-5.14.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_auth_web-5.15.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_core-2.24.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_core-3.13.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.2.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_core_web-2.10.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_core_web-2.23.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_messaging-14.7.10/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_messaging-15.2.6/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_messaging-15.2.7/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_messaging_platform_interface-4.5.18/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_messaging_platform_interface-4.6.6/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_messaging_platform_interface-4.6.7/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_messaging_web-3.10.6/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_messaging_web-3.10.7/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_messaging_web-3.5.18/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_storage-12.4.6/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_storage-12.4.7/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_storage_platform_interface-5.2.6/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_storage_platform_interface-5.2.7/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_storage_web-3.10.13/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_storage_web-3.10.14/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/fixnum-1.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/fl_chart-0.68.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_bloc-8.1.6/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_blue_plus-1.32.11/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_launcher_icons-0.14.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_lints-2.0.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_lints-4.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_local_notifications-19.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_local_notifications-19.2.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_local_notifications-19.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-6.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_local_notifications_platform_interface-9.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_local_notifications_platform_interface-9.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_local_notifications_windows-1.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_screenutil-5.9.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_slidable-4.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_svg-2.0.10+1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_svg-2.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_svg-2.2.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/font_awesome_flutter-10.8.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/fpdart-2.0.0-dev.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/freezed-2.5.7/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/freezed-2.5.8/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/freezed_annotation-2.4.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/frontend_server_client-4.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/get_it-7.7.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/get_it-8.0.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/glob-2.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/glob-2.1.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/google_fonts-6.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/google_identity_services_web-0.3.3+1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/google_identity_services_web-0.3.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/google_sign_in-6.2.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/google_sign_in-6.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/google_sign_in_android-6.2.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/google_sign_in_android-6.2.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/google_sign_in_ios-5.7.8/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/google_sign_in_ios-5.8.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/google_sign_in_platform_interface-2.5.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/google_sign_in_web-0.12.4+4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/graphs-2.3.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/hive-2.2.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/hive_ce-2.11.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/hive_ce-2.11.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/hive_ce_flutter-2.3.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/hive_generator-2.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/html-0.15.6/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/http-1.2.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/http-1.2.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/http-1.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/http-1.4.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/http_multi_server-3.2.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/http_multi_server-3.2.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/http_parser-4.0.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/image-4.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/image_picker-1.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/injectable-2.4.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/injectable-2.5.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/injectable_generator-2.6.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/injectable_generator-2.7.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/injector-4.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/internet_connection_checker_plus-2.7.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/intl-0.19.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/io-1.0.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/io-1.0.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/isolate_channel-0.2.2+1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/isolate_channel-0.2.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/js-0.6.7/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/js-0.7.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/js-0.7.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/json_serializable-6.8.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/json_serializable-6.9.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/json_serializable-6.9.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/json_serializable-6.9.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/leak_tracker-10.0.8/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/lints-2.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/lints-4.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/logging-1.2.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/logging-1.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/lottie-3.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/macros-0.1.3-main.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/matcher-0.12.17/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/melos-3.4.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/melos-6.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/melos-6.3.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/meta-1.16.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/meta-1.17.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/mime-1.0.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/mime-2.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/mockito-5.4.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/mocktail-1.0.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/mustache_template-2.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/nested-1.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/nm-0.5.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/node_preamble-2.0.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/os_detect-2.0.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/package_config-2.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/package_config-2.2.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/package_info_plus_platform_interface-3.2.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path-1.9.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path_parsing-1.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path_parsing-1.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider-2.1.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider-2.1.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_android-2.2.16/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_android-2.2.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_foundation-2.3.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_windows-2.2.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/patrol-3.14.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/patrol_finders-2.7.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/patrol_log-0.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/platform-3.1.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/platform-3.1.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/platform-3.1.6/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/pool-1.5.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/process-4.2.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/process-5.0.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/process-5.0.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/process-5.0.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/prompts-2.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/provider-6.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/provider-6.1.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/provider-6.1.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/pub_semver-2.1.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/pub_semver-2.2.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/pub_updater-0.3.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/pub_updater-0.4.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/pubspec-2.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/pubspec_parse-1.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/pubspec_parse-1.5.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/quiver-3.2.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/quiver-3.2.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/recase-4.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/rive-0.13.20/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/rive_common-0.4.15/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/rxdart-0.27.7/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/share_plus-10.1.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/share_plus_platform_interface-5.0.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences-2.3.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_android-2.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.8/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shelf-1.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shelf-1.4.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shelf_packages_handler-3.0.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shelf_static-1.1.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shelf_web_socket-2.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shelf_web_socket-3.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/showcaseview-3.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/sign_in_with_apple-6.1.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/sign_in_with_apple_platform_interface-1.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/sign_in_with_apple_web-2.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/simple_gesture_detector-0.2.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/skeletonizer-1.4.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/source_gen-1.5.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/source_gen-2.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/source_helper-1.3.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/source_helper-1.3.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/source_map_stack_trace-2.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/source_maps-0.10.13/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/stream_transform-2.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/sync_http-0.3.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-25.2.7/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/syncfusion_flutter_gauges-25.2.7/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/synchronized-3.3.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/table_calendar-3.1.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/test-1.25.15/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/test_api-0.7.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/test_core-0.6.8/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/timeline_tile-2.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/timezone-0.10.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/timezone-0.10.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/timing-1.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/timing-1.0.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/typed_data-1.3.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/upgrader-11.4.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/uri-1.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/vector_graphics-1.1.11+1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/vector_graphics-1.1.18/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/vector_graphics-1.1.19/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/vector_graphics_codec-1.1.11+1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/vector_graphics_codec-1.1.13/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.11+1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.17/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/version-3.0.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/video_player-2.10.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/video_player_android-2.8.7/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/video_player_avfoundation-2.7.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/video_player_platform_interface-6.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/video_player_web-2.3.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/vm_service-14.3.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/wakelock_plus-1.3.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/wakelock_plus_platform_interface-1.2.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/watcher-1.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/watcher-1.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/web-0.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/web-1.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/web-1.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/web_socket-0.1.6/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/web_socket-1.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/webdriver-3.0.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/webkit_inspection_protocol-1.2.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/win32-5.13.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/win32-5.2.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/win32_registry-2.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/xdg_directories-1.0.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/xml-6.5.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/yaml-3.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/yaml-3.1.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/yaml_edit-2.2.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/yaml_edit-2.2.2/lib" />
      <root url="file://$USER_HOME$/Development/flutter/bin/cache/dart-sdk/pkg/_macros/lib" />
      <root url="file://$USER_HOME$/Development/flutter/bin/cache/pkg/sky_engine/lib" />
      <root url="file://$USER_HOME$/Development/flutter/packages/flutter/lib" />
      <root url="file://$USER_HOME$/Development/flutter/packages/flutter_driver/lib" />
      <root url="file://$USER_HOME$/Development/flutter/packages/flutter_test/lib" />
      <root url="file://$USER_HOME$/Development/flutter/packages/flutter_web_plugins/lib" />
      <root url="file://$USER_HOME$/Development/flutter/packages/fuchsia_remote_debug_protocol/lib" />
      <root url="file://$USER_HOME$/Development/flutter/packages/integration_test/lib" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>