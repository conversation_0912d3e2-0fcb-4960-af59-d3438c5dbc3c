<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="92e1b2d5-5e6f-4198-bbe8-6411961b8a08" name="Changes" comment="test" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
        <option value="Dart File" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="feature/remote" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitHubPullRequestSearchHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPEN&quot;,
    &quot;assignee&quot;: &quot;rovianadz&quot;
  }
}</component>
  <component name="GithubPullRequestsUISettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;url&quot;: &quot;**************:Junotechnologies/Juno_app.git&quot;,
    &quot;accountId&quot;: &quot;61be918a-7d4d-4380-944d-76f11310cca9&quot;
  }
}</component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2b6gAXVdvtAZvvZwUG0i8mGVCo4" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ASKED_ADD_EXTERNAL_FILES": "true",
    "Dart Test.Tests in device_control_heat_bloc_test.dart.executor": "Run",
    "Dart Test.Tests in device_control_heat_watcher_bloc_test.dart.executor": "Run",
    "Dart Test.Tests in remote_test.dart.executor": "Run",
    "Flutter Test.AccountWatcherBloc.executor": "Run",
    "Flutter Test.Google +.executor": "Run",
    "Flutter Test.Google -.executor": "Run",
    "Flutter Test.Profile  My Profile test.executor": "Run",
    "Flutter Test.Sign up with Apple.executor": "Run",
    "Flutter Test.Sign up with Google.executor": "Run",
    "Flutter Test.cancels previous subscription when a new watchAllStarted event is added.executor": "Run",
    "Flutter Test.tests in account_management_bloc_test.dart.executor": "Run",
    "Flutter Test.tests in account_watcher_bloc_test.dart.executor": "Run",
    "Flutter Test.tests in bluetooth_service_bloc_test.dart.executor": "Run",
    "Flutter Test.tests in help_center_contact_details_bloc_test.dart.executor": "Run",
    "Flutter Test.tests in help_center_faq_bloc_test.dart.executor": "Debug",
    "Flutter Test.tests in help_center_model_test.dart.executor": "Run",
    "Flutter Test.tests in help_center_videos_bloc_test.dart.executor": "Run",
    "Flutter Test.tests in login_test.dart.executor": "Run",
    "Flutter Test.tests in manage_period_tracking_bloc_test.dart.executor": "Run",
    "Flutter Test.tests in medication_form_bloc_test.dart.executor": "Run",
    "Flutter Test.tests in period_tracking_watcher_bloc_test.dart.executor": "Run",
    "Flutter Test.tests in remote_test.dart.executor": "Run",
    "Flutter Test.tests in test_bundle.dart.executor": "Run",
    "Flutter Test.tests in update_health_data_bloc_test.dart.executor": "Run",
    "Flutter.calendar.dart.executor": "Run",
    "Flutter.get_started_page.dart.executor": "Run",
    "Flutter.main.dart (1).executor": "Run",
    "Flutter.main_dev.dart.executor": "Run",
    "Flutter.main_prod.dart.executor": "Run",
    "PROJECT_TRUSTED_KEY": "true",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.cidr.known.project.marker": "true",
    "RunOnceActivity.git.unshallow": "true",
    "RunOnceActivity.readMode.enableVisualFormatting": "true",
    "ScreenshotViewer.SavePath": "/Users/<USER>/Downloads",
    "cf.first.check.clang-format": "false",
    "cidr.known.project.marker": "true",
    "com.google.services.firebase.aqiPopupShown": "true",
    "git-widget-placeholder": "#31 on feature/remote",
    "io.flutter.reload.alreadyRun": "true",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "/Users/<USER>/Documents/Projects/Pravah",
    "project.structure.last.edited": "Project",
    "project.structure.proportion": "0.15",
    "project.structure.side.proportion": "0.2",
    "settings.editor.selected.configurable": "com.android.studio.ml.bot.mainConfigurable",
    "show.migrate.to.gradle.popup": "false",
    "structure.view.defaults.are.configured": "true",
    "two.files.diff.last.used.file": "/Users/<USER>/Desktop/files old/authentication/lib/application/bloc/sign_in/sign_in_bloc.dart",
    "two.files.diff.last.used.folder": "/Users/<USER>/Documents/JunoPlus/authentication/lib"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$" />
      <recent name="$PROJECT_DIR$/app/ios/config/Prod" />
      <recent name="$PROJECT_DIR$/app/lib/services" />
      <recent name="$PROJECT_DIR$/app" />
      <recent name="$PROJECT_DIR$/app/assets/rive" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/app/ios/config/Dev" />
      <recent name="$PROJECT_DIR$/analytics/lib/domain/models" />
      <recent name="$PROJECT_DIR$/notifications/lib/application/custom_notification_stream" />
      <recent name="$PROJECT_DIR$/account_management/lib/application/therapy_management_bloc" />
      <recent name="$PROJECT_DIR$/app/integration_test" />
    </key>
  </component>
  <component name="RunManager" selected="Flutter.main_dev.dart">
    <configuration name="main.dart" type="FlutterRunConfigurationType" factoryName="Flutter">
      <option name="filePath" value="$PROJECT_DIR$/lib/main.dart" />
      <method v="2" />
    </configuration>
    <configuration name="main_dev.dart" type="FlutterRunConfigurationType" factoryName="Flutter" nameIsGenerated="true">
      <option name="buildFlavor" value="dev" />
      <option name="filePath" value="$PROJECT_DIR$/app/lib/main_dev.dart" />
      <method v="2" />
    </configuration>
    <configuration name="tests in bluetooth_service_bloc_test.dart" type="FlutterTestConfigType" factoryName="Flutter Test" temporary="true" nameIsGenerated="true">
      <option name="testFile" value="$PROJECT_DIR$/bluetooth/test/bluetooth_service_bloc_test.dart" />
      <option name="useRegexp" value="false" />
      <method v="2" />
    </configuration>
    <configuration name="tests in help_center_contact_details_bloc_test.dart" type="FlutterTestConfigType" factoryName="Flutter Test" temporary="true" nameIsGenerated="true">
      <option name="testFile" value="$PROJECT_DIR$/help_center/test/help_center_contact_details_bloc_test.dart" />
      <option name="useRegexp" value="false" />
      <method v="2" />
    </configuration>
    <configuration name="tests in help_center_model_test.dart" type="FlutterTestConfigType" factoryName="Flutter Test" temporary="true" nameIsGenerated="true">
      <option name="testFile" value="$PROJECT_DIR$/help_center/test/help_center_model_test.dart" />
      <option name="useRegexp" value="false" />
      <method v="2" />
    </configuration>
    <configuration name="tests in help_center_videos_bloc_test.dart" type="FlutterTestConfigType" factoryName="Flutter Test" temporary="true" nameIsGenerated="true">
      <option name="testFile" value="$PROJECT_DIR$/help_center/test/help_center_videos_bloc_test.dart" />
      <option name="useRegexp" value="false" />
      <method v="2" />
    </configuration>
    <configuration name="tests in login_test.dart" type="FlutterTestConfigType" factoryName="Flutter Test" temporary="true" nameIsGenerated="true">
      <option name="testFile" value="$PROJECT_DIR$/app/integration_test/authentication/login_test.dart" />
      <option name="useRegexp" value="false" />
      <option name="additionalArgs" value="" />
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="Flutter.main.dart" />
      <item itemvalue="Flutter.main_dev.dart" />
      <item itemvalue="Flutter Test.Flutter Test -&gt; 'pravah'" />
      <item itemvalue="Flutter Test.Flutter Test -&gt; 'account_management'" />
      <item itemvalue="Flutter Test.Flutter Test -&gt; 'authentication'" />
      <item itemvalue="Flutter Test.Flutter Test -&gt; 'design_system'" />
      <item itemvalue="Flutter Test.Flutter Test -&gt; 'notifications'" />
      <item itemvalue="Flutter Test.tests in bluetooth_service_bloc_test.dart" />
      <item itemvalue="Flutter Test.tests in help_center_contact_details_bloc_test.dart" />
      <item itemvalue="Flutter Test.tests in help_center_model_test.dart" />
      <item itemvalue="Flutter Test.tests in help_center_videos_bloc_test.dart" />
      <item itemvalue="Flutter Test.tests in login_test.dart" />
      <item itemvalue="Shell Script.Melos Run -&gt; 'test:update-goldens:all'" />
      <item itemvalue="Shell Script.Melos Run -&gt; 'run:dev-app'" />
      <item itemvalue="Shell Script.Melos Run -&gt; 'test:with-lcov-coverage:all'" />
      <item itemvalue="Shell Script.Melos Run -&gt; 'test:with-html-coverage:all'" />
      <item itemvalue="Shell Script.Melos Run -&gt; 'run:uat-app'" />
      <item itemvalue="Shell Script.Melos Run -&gt; 'build:clean'" />
      <item itemvalue="Shell Script.Melos Run -&gt; 'build:codegen:build:all'" />
      <item itemvalue="Shell Script.Melos Run -&gt; 'extract-dependencies'" />
      <item itemvalue="Shell Script.Melos Run -&gt; 'test:generate-html-coverage'" />
      <item itemvalue="Shell Script.Melos -&gt; Clean Workspace" />
      <item itemvalue="Shell Script.Melos Run -&gt; 'build:build-prod-app'" />
      <item itemvalue="Shell Script.Melos Run -&gt; 'build:clean:all'" />
      <item itemvalue="Shell Script.Melos Run -&gt; 'build:pub_get:all'" />
      <item itemvalue="Shell Script.Melos -&gt; Bootstrap Workspace" />
      <item itemvalue="Shell Script.Melos Run -&gt; 'quality:analyze'" />
      <item itemvalue="Shell Script.Melos Run -&gt; 'test'" />
      <item itemvalue="Shell Script.Melos Run -&gt; 'build:build-uat-app'" />
      <item itemvalue="Shell Script.Melos Run -&gt; 'test:with-html-coverage:all:auto-open'" />
      <item itemvalue="Shell Script.Melos Run -&gt; 'quality:analyze:all'" />
      <item itemvalue="Shell Script.Melos Run -&gt; 'test:combine-coverage'" />
      <item itemvalue="Shell Script.Melos Run -&gt; 'build:build-dev-app'" />
      <item itemvalue="Shell Script.Melos Run -&gt; 'test:update-goldens'" />
      <item itemvalue="Shell Script.Melos Run -&gt; 'test:all'" />
      <item itemvalue="Shell Script.Melos Run -&gt; 'build:codegen:build'" />
      <item itemvalue="Shell Script.Melos Run -&gt; 'quality:dcm-checks'" />
      <item itemvalue="Shell Script.Melos Run -&gt; 'test:with-html-coverage'" />
      <item itemvalue="Shell Script.Melos Run -&gt; 'test:with-lcov-coverage'" />
      <item itemvalue="Shell Script.Melos Run -&gt; 'quality:dcm-checks:all'" />
      <item itemvalue="Shell Script.Melos Run -&gt; 'run:prod-app'" />
      <item itemvalue="Shell Script.Melos Run -&gt; 'build:codegen:watch'" />
    </list>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="SwiftWorkspaceSettings">
    <option name="detectedToolchain" value="true" />
    <option name="toolchainPathValue" value="/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain" />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="92e1b2d5-5e6f-4198-bbe8-6411961b8a08" name="Changes" comment="" />
      <created>1705540801081</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1705540801081</updated>
    </task>
    <task id="LOCAL-00001" summary="test">
      <option name="closed" value="true" />
      <created>1706215895215</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1706215895215</updated>
    </task>
    <task id="LOCAL-00002" summary="test">
      <option name="closed" value="true" />
      <created>1706216177763</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1706216177763</updated>
    </task>
    <task id="LOCAL-00003" summary="test">
      <option name="closed" value="true" />
      <created>1706217052277</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1706217052277</updated>
    </task>
    <task id="LOCAL-00004" summary="test">
      <option name="closed" value="true" />
      <created>1706217885508</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1706217885508</updated>
    </task>
    <task id="LOCAL-00005" summary="test">
      <option name="closed" value="true" />
      <created>1706222759304</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1706222759304</updated>
    </task>
    <task id="LOCAL-00006" summary="test">
      <option name="closed" value="true" />
      <created>1706223858701</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1706223858701</updated>
    </task>
    <task id="LOCAL-00007" summary="test">
      <option name="closed" value="true" />
      <created>1706224229392</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1706224229392</updated>
    </task>
    <task id="LOCAL-00008" summary="test">
      <option name="closed" value="true" />
      <created>1706225114912</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1706225114912</updated>
    </task>
    <task id="LOCAL-00009" summary="test">
      <option name="closed" value="true" />
      <created>1706225403233</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1706225403233</updated>
    </task>
    <task id="LOCAL-00010" summary="test">
      <option name="closed" value="true" />
      <created>1706225596896</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1706225596896</updated>
    </task>
    <task id="LOCAL-00011" summary="test">
      <option name="closed" value="true" />
      <created>1706226012326</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1706226012326</updated>
    </task>
    <task id="LOCAL-00012" summary="test">
      <option name="closed" value="true" />
      <created>1706226724179</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1706226724179</updated>
    </task>
    <task id="LOCAL-00013" summary="test">
      <option name="closed" value="true" />
      <created>1706227267776</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1706227267776</updated>
    </task>
    <task id="LOCAL-00014" summary="test">
      <option name="closed" value="true" />
      <created>1706227899823</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1706227899823</updated>
    </task>
    <task id="LOCAL-00015" summary="test">
      <option name="closed" value="true" />
      <created>1706229725446</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1706229725447</updated>
    </task>
    <task id="LOCAL-00016" summary="test">
      <option name="closed" value="true" />
      <created>1706229908286</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1706229908286</updated>
    </task>
    <task id="LOCAL-00017" summary="test">
      <option name="closed" value="true" />
      <created>1706230028330</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1706230028330</updated>
    </task>
    <task id="LOCAL-00018" summary="test">
      <option name="closed" value="true" />
      <created>1706233333357</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1706233333357</updated>
    </task>
    <task id="LOCAL-00019" summary="test">
      <option name="closed" value="true" />
      <created>1706233907554</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1706233907554</updated>
    </task>
    <task id="LOCAL-00020" summary="test">
      <option name="closed" value="true" />
      <created>1706234799851</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1706234799851</updated>
    </task>
    <task id="LOCAL-00021" summary="test">
      <option name="closed" value="true" />
      <created>1706378031655</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1706378031655</updated>
    </task>
    <task id="LOCAL-00022" summary="test">
      <option name="closed" value="true" />
      <created>1707771206828</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1707771206828</updated>
    </task>
    <task id="LOCAL-00023" summary="test">
      <option name="closed" value="true" />
      <created>1707772704164</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1707772704164</updated>
    </task>
    <task id="LOCAL-00024" summary="test">
      <option name="closed" value="true" />
      <created>1707773935476</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1707773935476</updated>
    </task>
    <task id="LOCAL-00025" summary="test">
      <option name="closed" value="true" />
      <created>1707866151382</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1707866151382</updated>
    </task>
    <task id="LOCAL-00026" summary="test">
      <option name="closed" value="true" />
      <created>1707866715656</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1707866715656</updated>
    </task>
    <task id="LOCAL-00027" summary="test">
      <option name="closed" value="true" />
      <created>1707867378205</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1707867378205</updated>
    </task>
    <task id="LOCAL-00028" summary="test">
      <option name="closed" value="true" />
      <created>1707867519201</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1707867519201</updated>
    </task>
    <task id="LOCAL-00029" summary="test">
      <option name="closed" value="true" />
      <created>1707868107986</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1707868107986</updated>
    </task>
    <task id="LOCAL-00030" summary="test">
      <option name="closed" value="true" />
      <created>1707943685974</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1707943685974</updated>
    </task>
    <task id="LOCAL-00031" summary="test">
      <option name="closed" value="true" />
      <created>1707944171326</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1707944171326</updated>
    </task>
    <task id="LOCAL-00032" summary="test">
      <option name="closed" value="true" />
      <created>1707944291850</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1707944291850</updated>
    </task>
    <task id="LOCAL-00033" summary="test">
      <option name="closed" value="true" />
      <created>1707945384627</created>
      <option name="number" value="00033" />
      <option name="presentableId" value="LOCAL-00033" />
      <option name="project" value="LOCAL" />
      <updated>1707945384627</updated>
    </task>
    <task id="LOCAL-00034" summary="test">
      <option name="closed" value="true" />
      <created>1707946353533</created>
      <option name="number" value="00034" />
      <option name="presentableId" value="LOCAL-00034" />
      <option name="project" value="LOCAL" />
      <updated>1707946353533</updated>
    </task>
    <task id="LOCAL-00035" summary="test">
      <option name="closed" value="true" />
      <created>1707948274297</created>
      <option name="number" value="00035" />
      <option name="presentableId" value="LOCAL-00035" />
      <option name="project" value="LOCAL" />
      <updated>1707948274297</updated>
    </task>
    <task id="LOCAL-00036" summary="test">
      <option name="closed" value="true" />
      <created>1707948734795</created>
      <option name="number" value="00036" />
      <option name="presentableId" value="LOCAL-00036" />
      <option name="project" value="LOCAL" />
      <updated>1707948734795</updated>
    </task>
    <task id="LOCAL-00037" summary="test">
      <option name="closed" value="true" />
      <created>1707949339531</created>
      <option name="number" value="00037" />
      <option name="presentableId" value="LOCAL-00037" />
      <option name="project" value="LOCAL" />
      <updated>1707949339531</updated>
    </task>
    <task id="LOCAL-00038" summary="test">
      <option name="closed" value="true" />
      <created>1709061291104</created>
      <option name="number" value="00038" />
      <option name="presentableId" value="LOCAL-00038" />
      <option name="project" value="LOCAL" />
      <updated>1709061291104</updated>
    </task>
    <task id="LOCAL-00039" summary="test">
      <option name="closed" value="true" />
      <created>1709068671437</created>
      <option name="number" value="00039" />
      <option name="presentableId" value="LOCAL-00039" />
      <option name="project" value="LOCAL" />
      <updated>1709068671437</updated>
    </task>
    <task id="LOCAL-00040" summary="t&#10;est">
      <option name="closed" value="true" />
      <created>1709070366966</created>
      <option name="number" value="00040" />
      <option name="presentableId" value="LOCAL-00040" />
      <option name="project" value="LOCAL" />
      <updated>1709070366966</updated>
    </task>
    <task id="LOCAL-00041" summary="test">
      <option name="closed" value="true" />
      <created>1709072533565</created>
      <option name="number" value="00041" />
      <option name="presentableId" value="LOCAL-00041" />
      <option name="project" value="LOCAL" />
      <updated>1709072533565</updated>
    </task>
    <task id="LOCAL-00042" summary="test">
      <option name="closed" value="true" />
      <created>1709136713258</created>
      <option name="number" value="00042" />
      <option name="presentableId" value="LOCAL-00042" />
      <option name="project" value="LOCAL" />
      <updated>1709136713258</updated>
    </task>
    <task id="LOCAL-00043" summary="test">
      <option name="closed" value="true" />
      <created>1709138603959</created>
      <option name="number" value="00043" />
      <option name="presentableId" value="LOCAL-00043" />
      <option name="project" value="LOCAL" />
      <updated>1709138603959</updated>
    </task>
    <task id="LOCAL-00044" summary="test">
      <option name="closed" value="true" />
      <created>1709140629381</created>
      <option name="number" value="00044" />
      <option name="presentableId" value="LOCAL-00044" />
      <option name="project" value="LOCAL" />
      <updated>1709140629381</updated>
    </task>
    <task id="LOCAL-00045" summary="test">
      <option name="closed" value="true" />
      <created>1710434171011</created>
      <option name="number" value="00045" />
      <option name="presentableId" value="LOCAL-00045" />
      <option name="project" value="LOCAL" />
      <updated>1710434171011</updated>
    </task>
    <option name="localTasksCounter" value="46" />
    <servers />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="CUSTOM_BOOLEAN_PROPERTIES">
                <map>
                  <entry key="Show.Git.Branches" value="false" />
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="ADD_EXTERNAL_FILES_SILENTLY" value="true" />
    <MESSAGE value="t&#10;est" />
    <MESSAGE value="test" />
    <option name="LAST_COMMIT_MESSAGE" value="test" />
  </component>
  <component name="WfRunsListSearchHistory">{
  &quot;history&quot;: [
    {
      &quot;branch&quot;: &quot;feature/bluetooth&quot;,
      &quot;currentBranchFilter&quot;: true
    },
    {
      &quot;branch&quot;: &quot;feature/bluetooth&quot;,
      &quot;currentBranchFilter&quot;: true
    },
    {
      &quot;branch&quot;: &quot;feature/bluetooth&quot;,
      &quot;currentBranchFilter&quot;: true
    },
    {
      &quot;branch&quot;: &quot;feature/help_center&quot;,
      &quot;currentBranchFilter&quot;: true
    },
    {
      &quot;branch&quot;: &quot;feature/help_center&quot;,
      &quot;currentBranchFilter&quot;: true
    },
    {
      &quot;branch&quot;: &quot;feature/notifications&quot;,
      &quot;currentBranchFilter&quot;: true
    },
    {
      &quot;branch&quot;: &quot;feature/notifications&quot;,
      &quot;currentBranchFilter&quot;: true
    },
    {
      &quot;branch&quot;: &quot;feature/notifications&quot;,
      &quot;currentBranchFilter&quot;: true
    },
    {
      &quot;branch&quot;: &quot;feature/notifications&quot;,
      &quot;currentBranchFilter&quot;: true
    },
    {
      &quot;branch&quot;: &quot;feature/account_management&quot;,
      &quot;currentBranchFilter&quot;: true
    },
    {
      &quot;branch&quot;: &quot;feature/account_management&quot;,
      &quot;currentBranchFilter&quot;: true
    },
    {
      &quot;branch&quot;: &quot;feature/help_center&quot;,
      &quot;currentBranchFilter&quot;: true
    },
    {
      &quot;branch&quot;: &quot;feature/help_center&quot;,
      &quot;currentBranchFilter&quot;: true
    },
    {
      &quot;branch&quot;: &quot;feature/notifications&quot;,
      &quot;currentBranchFilter&quot;: true
    },
    {
      &quot;branch&quot;: &quot;feature/notifications&quot;,
      &quot;currentBranchFilter&quot;: true
    },
    {
      &quot;branch&quot;: &quot;feature/notifications&quot;,
      &quot;currentBranchFilter&quot;: true
    },
    {
      &quot;branch&quot;: &quot;feature/account_management&quot;,
      &quot;currentBranchFilter&quot;: true
    },
    {
      &quot;branch&quot;: &quot;feature/bluetooth&quot;,
      &quot;currentBranchFilter&quot;: true
    },
    {
      &quot;branch&quot;: &quot;feature/help_center&quot;,
      &quot;currentBranchFilter&quot;: true
    },
    {
      &quot;branch&quot;: &quot;feature/notifications&quot;,
      &quot;currentBranchFilter&quot;: true
    },
    {
      &quot;branch&quot;: &quot;feature/account_management&quot;,
      &quot;currentBranchFilter&quot;: true
    },
    {
      &quot;branch&quot;: &quot;develop&quot;,
      &quot;currentBranchFilter&quot;: true
    },
    {
      &quot;branch&quot;: &quot;feature/account_management&quot;,
      &quot;currentBranchFilter&quot;: true
    },
    {
      &quot;branch&quot;: &quot;feature/account_management&quot;,
      &quot;currentBranchFilter&quot;: true
    },
    {
      &quot;branch&quot;: &quot;feature/account_management&quot;,
      &quot;currentBranchFilter&quot;: true
    },
    {
      &quot;branch&quot;: &quot;feature/account_management&quot;,
      &quot;currentBranchFilter&quot;: true
    },
    {
      &quot;branch&quot;: &quot;feature/account_management&quot;,
      &quot;currentBranchFilter&quot;: true
    }
  ],
  &quot;lastFilter&quot;: {
    &quot;branch&quot;: &quot;feature/account_management&quot;,
    &quot;currentBranchFilter&quot;: true
  }
}</component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="Dart">
          <url>file://$PROJECT_DIR$/app/lib/app.dart</url>
          <line>130</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="../../../../Documents/JunoPlus/authentication/coverage/lcov.info" NAME="tests in sigin_with_email_wrapper_test.dart Coverage Results" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="FlutterCoverageRunner" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" />
  </component>
</project>