[{"type": {"import": "package:flutter/material.dart", "name": "GlobalKey", "isNullable": false, "isRecordType": false, "nameInRecord": null, "typeArguments": [{"import": "package:flutter/material.dart", "name": "NavigatorState", "isNullable": false, "isRecordType": false, "nameInRecord": null, "otherImports": ["package:flutter/widgets.dart", "package:flutter/cupertino.dart", "package:flutter/src/widgets/navigator.dart"]}], "otherImports": ["package:flutter/widgets.dart", "package:flutter/cupertino.dart", "package:flutter/src/widgets/framework.dart"]}, "typeImpl": {"import": "package:flutter/material.dart", "name": "GlobalKey", "isNullable": false, "isRecordType": false, "nameInRecord": null, "typeArguments": [{"import": "package:flutter/material.dart", "name": "NavigatorState", "isNullable": false, "isRecordType": false, "nameInRecord": null, "otherImports": ["package:flutter/widgets.dart", "package:flutter/cupertino.dart", "package:flutter/src/widgets/navigator.dart"]}], "otherImports": ["package:flutter/widgets.dart", "package:flutter/cupertino.dart", "package:flutter/src/widgets/framework.dart"]}, "isAsync": false, "postConstructReturnsSelf": false, "preResolve": false, "canBeConst": false, "injectableType": 1, "moduleConfig": {"isAbstract": false, "isMethod": false, "type": {"import": "package:pravah/di_modules.dart", "name": "AppModule", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "initializerName": "navigator<PERSON><PERSON>"}, "dependsOn": [], "environments": [], "dependencies": [], "constructorName": "navigator<PERSON><PERSON>", "orderPosition": 0}]