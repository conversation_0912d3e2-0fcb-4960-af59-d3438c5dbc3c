{"routes": [{"name": null, "pathParams": [], "pageType": {"import": "package:pravah/pages/dashboard/daily_symptom_tracking_page.dart", "name": "DailySymptomTrackingPage", "isNullable": false, "isRecordType": false}, "className": "DailySymptomTrackingPage", "parameters": [{"type": {"import": "package:flutter/material.dart", "name": "Key", "isNullable": true, "isRecordType": false}, "name": "key", "alias": "key", "isPositional": false, "isOptional": true, "hasRequired": false, "isRequired": false, "isNamed": true, "isPathParam": false, "isUrlFragment": false, "isInheritedPathParam": false, "isQueryParam": false, "defaultValueCode": null}, {"type": {"import": null, "name": "DateTime", "isNullable": true, "isRecordType": false}, "name": "initialDate", "alias": "initialDate", "isPositional": false, "isOptional": true, "hasRequired": false, "isRequired": false, "isNamed": true, "isPathParam": false, "isUrlFragment": false, "isInheritedPathParam": false, "isQueryParam": false, "defaultValueCode": null}], "hasWrappedRoute": false, "hasConstConstructor": false, "deferredLoading": null}], "inputPath": "lib/pages/dashboard/daily_symptom_tracking_page.dart", "inputHash": 0}