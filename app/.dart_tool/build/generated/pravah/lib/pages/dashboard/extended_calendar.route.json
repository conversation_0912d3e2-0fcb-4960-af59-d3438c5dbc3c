{"routes": [{"name": null, "pathParams": [], "pageType": {"import": "package:pravah/pages/dashboard/extended_calendar.dart", "name": "ExtendedCalenderPage", "isNullable": false, "isRecordType": false}, "className": "ExtendedCalenderPage", "parameters": [], "hasWrappedRoute": false, "hasConstConstructor": true, "deferredLoading": null}], "inputPath": "lib/pages/dashboard/extended_calendar.dart", "inputHash": 0}