{"routes": [{"name": null, "pathParams": [], "pageType": {"import": "package:pravah/pages/dashboard/new_dashboard_page.dart", "name": "NewDashboardPage", "isNullable": false, "isRecordType": false}, "className": "NewDashboardPage", "parameters": [], "hasWrappedRoute": false, "hasConstConstructor": false, "deferredLoading": null}], "inputPath": "lib/pages/dashboard/new_dashboard_page.dart", "inputHash": 0}