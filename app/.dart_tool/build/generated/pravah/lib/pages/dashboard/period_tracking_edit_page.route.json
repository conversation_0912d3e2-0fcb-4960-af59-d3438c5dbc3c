{"routes": [{"name": null, "pathParams": [], "pageType": {"import": "package:pravah/pages/dashboard/period_tracking_edit_page.dart", "name": "PeriodTrackingEditPage", "isNullable": false, "isRecordType": false}, "className": "PeriodTrackingEditPage", "parameters": [{"type": {"import": "package:flutter/material.dart", "name": "Key", "isNullable": true, "isRecordType": false}, "name": "key", "alias": "key", "isPositional": false, "isOptional": true, "hasRequired": false, "isRequired": false, "isNamed": true, "isPathParam": false, "isUrlFragment": false, "isInheritedPathParam": false, "isQueryParam": false, "defaultValueCode": null}, {"type": {"import": null, "name": "int", "isNullable": true, "isRecordType": false}, "name": "initialYear", "alias": "initialYear", "isPositional": false, "isOptional": true, "hasRequired": false, "isRequired": false, "isNamed": true, "isPathParam": false, "isUrlFragment": false, "isInheritedPathParam": false, "isQueryParam": false, "defaultValueCode": null}], "hasWrappedRoute": false, "hasConstConstructor": false, "deferredLoading": null}], "inputPath": "lib/pages/dashboard/period_tracking_edit_page.dart", "inputHash": 0}