{"routes": [{"name": null, "pathParams": [], "pageType": {"import": "package:pravah/pages/dashboard/period_tracking_view_page.dart", "name": "PeriodTrackingViewPage", "isNullable": false, "isRecordType": false}, "className": "PeriodTrackingViewPage", "parameters": [], "hasWrappedRoute": false, "hasConstConstructor": true, "deferredLoading": null}], "inputPath": "lib/pages/dashboard/period_tracking_view_page.dart", "inputHash": 0}