{"routes": [{"name": null, "pathParams": [], "pageType": {"import": "package:pravah/pages/login/email_verification_page.dart", "name": "EmailVerificationPage", "isNullable": false, "isRecordType": false}, "className": "EmailVerificationPage", "parameters": [], "hasWrappedRoute": false, "hasConstConstructor": true, "deferredLoading": null}], "inputPath": "lib/pages/login/email_verification_page.dart", "inputHash": 0}