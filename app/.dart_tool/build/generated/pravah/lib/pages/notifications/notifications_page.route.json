{"routes": [{"name": null, "pathParams": [], "pageType": {"import": "package:pravah/pages/notifications/notifications_page.dart", "name": "NotificationsPage", "isNullable": false, "isRecordType": false}, "className": "NotificationsPage", "parameters": [], "hasWrappedRoute": false, "hasConstConstructor": true, "deferredLoading": null}], "inputPath": "lib/pages/notifications/notifications_page.dart", "inputHash": 0}