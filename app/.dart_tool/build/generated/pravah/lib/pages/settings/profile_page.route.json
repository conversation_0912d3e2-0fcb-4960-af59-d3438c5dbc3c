{"routes": [{"name": null, "pathParams": [], "pageType": {"import": "package:pravah/pages/settings/profile_page.dart", "name": "ProfilePage", "isNullable": false, "isRecordType": false}, "className": "ProfilePage", "parameters": [{"type": {"import": "package:flutter/material.dart", "name": "Key", "isNullable": true, "isRecordType": false}, "name": "key", "alias": "key", "isPositional": false, "isOptional": true, "hasRequired": false, "isRequired": false, "isNamed": true, "isPathParam": false, "isUrlFragment": false, "isInheritedPathParam": false, "isQueryParam": false, "defaultValueCode": null}], "hasWrappedRoute": false, "hasConstConstructor": false, "deferredLoading": null}], "inputPath": "lib/pages/settings/profile_page.dart", "inputHash": 0}