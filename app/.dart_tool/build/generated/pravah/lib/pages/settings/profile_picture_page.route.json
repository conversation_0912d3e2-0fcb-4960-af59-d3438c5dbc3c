{"routes": [{"name": null, "pathParams": [], "pageType": {"import": "package:pravah/pages/settings/profile_picture_page.dart", "name": "ProfilePicturePage", "isNullable": false, "isRecordType": false}, "className": "ProfilePicturePage", "parameters": [], "hasWrappedRoute": false, "hasConstConstructor": false, "deferredLoading": null}], "inputPath": "lib/pages/settings/profile_picture_page.dart", "inputHash": 0}