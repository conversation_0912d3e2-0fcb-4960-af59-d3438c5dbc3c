{"configVersion": 2, "packages": [{"name": "_fe_analyzer_shared", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/_fe_analyzer_shared-83.0.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "_flutterfire_internals", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/_flutterfire_internals-1.3.56", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "account_management", "rootUri": "../../account_management", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "analyzer", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/analyzer-7.4.6", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "another_xlider", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/another_xlider-3.0.2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "archive", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "args", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "async", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "authentication", "rootUri": "../../authentication", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "auto_route", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/auto_route-10.1.0+1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "auto_route_generator", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/auto_route_generator-10.1.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "awesome_dialog", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/awesome_dialog-3.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "bloc", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/bloc-8.1.4", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "bloc_test", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/bloc_test-9.1.7", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "boolean_selector", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "build", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/build-2.5.4", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "build_config", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_config-1.1.2", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "build_daemon", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_daemon-4.0.4", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "build_resolvers", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_resolvers-2.5.4", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "build_runner", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_runner-2.5.4", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "build_runner_core", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_runner_core-9.1.2", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "built_collection", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/built_collection-5.1.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "built_value", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/built_value-8.10.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "cached_network_image", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "cached_network_image_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "cached_network_image_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_web-1.3.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "calendar_date_picker2", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/calendar_date_picker2-1.1.9", "packageUri": "lib/", "languageVersion": "2.16"}, {"name": "carousel_slider_x", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/carousel_slider_x-6.0.7", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "change_app_package_name", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/change_app_package_name-1.5.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "characters", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "checked_yaml", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.3", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "chewie", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/chewie-1.11.3", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "cli_config", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cli_config-0.2.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "cli_util", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cli_util-0.4.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "clock", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "cloud_firestore", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.6.8", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "cloud_firestore_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.9", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "cloud_firestore_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_web-4.4.9", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "cloud_functions", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cloud_functions-5.5.2", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "cloud_functions_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cloud_functions_platform_interface-5.8.3", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "cloud_functions_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cloud_functions_web-4.11.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "code_builder", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/code_builder-4.10.1", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "collection", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "connectivity_plus", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "connectivity_plus_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "convert", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/convert-3.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "coverage", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/coverage-1.14.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "cross_file", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "crypto", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "csslib", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "cupertino_icons", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "dart_style", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/dart_style-3.1.0", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "date_picker_plus", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/date_picker_plus-4.2.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "dbus", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "design_system", "rootUri": "../../design_system", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "device_info_plus", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-11.5.0", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "device_info_plus_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.3", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "diff_match_patch", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/diff_match_patch-0.4.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "dispose_scope", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/dispose_scope-2.1.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "equatable", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "fake_async", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "ffi", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "file", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "file_selector_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "file_selector_macos", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "file_selector_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "file_selector_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "firebase_app_check", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_app_check-0.3.2+7", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_app_check_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_app_check_platform_interface-0.1.1+7", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_app_check_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_app_check_web-0.2.0+11", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "firebase_auth", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-5.5.4", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_auth_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_auth_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_web-5.15.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "firebase_core", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.13.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_core_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_core_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.23.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "firebase_messaging", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-15.2.7", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_messaging_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_platform_interface-4.6.7", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_messaging_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_web-3.10.7", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "firebase_storage", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_storage-12.4.7", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_storage_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_storage_platform_interface-5.2.7", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_storage_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_storage_web-3.10.14", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "fixnum", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "fl_chart", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.68.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "flutter", "rootUri": "file:///Users/<USER>/Development/flutter/packages/flutter", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_bloc", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_bloc-8.1.6", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_blue_plus", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_blue_plus-1.32.11", "packageUri": "lib/", "languageVersion": "2.15"}, {"name": "flutter_cache_manager", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "flutter_driver", "rootUri": "file:///Users/<USER>/Development/flutter/packages/flutter_driver", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_hooks", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_launcher_icons", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_launcher_icons-0.14.4", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "flutter_lints", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-4.0.0", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "flutter_local_notifications", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-19.3.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "flutter_local_notifications_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-6.0.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "flutter_local_notifications_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_platform_interface-9.1.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "flutter_local_notifications_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_windows-1.0.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "flutter_plugin_android_lifecycle", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "flutter_screenutil", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_screenutil-5.9.3", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_slidable", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_slidable-4.0.0", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "flutter_svg", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_svg-2.2.0", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "flutter_test", "rootUri": "file:///Users/<USER>/Development/flutter/packages/flutter_test", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_web_plugins", "rootUri": "file:///Users/<USER>/Development/flutter/packages/flutter_web_plugins", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "fluttertoast", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.12", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "font_awesome_flutter", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/font_awesome_flutter-10.8.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "fpdart", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/fpdart-2.0.0-dev.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "freezed_annotation", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/freezed_annotation-2.4.4", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "frontend_server_client", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/frontend_server_client-4.0.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "fuchsia_remote_debug_protocol", "rootUri": "file:///Users/<USER>/Development/flutter/packages/fuchsia_remote_debug_protocol", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "get_it", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/get_it-7.7.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "glob", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/glob-2.1.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "google_fonts", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.1.0", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "google_identity_services_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_identity_services_web-0.3.3+1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "google_sign_in", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in-6.2.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "google_sign_in_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-6.2.1", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "google_sign_in_ios", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_ios-5.7.8", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "google_sign_in_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_platform_interface-2.5.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "google_sign_in_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_web-0.12.4+4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "graphs", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/graphs-2.3.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "hive_ce", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/hive_ce-2.11.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "hive_ce_flutter", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/hive_ce_flutter-2.3.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "html", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "http", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "http_multi_server", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_multi_server-3.2.2", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "http_parser", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image-4.3.0", "packageUri": "lib/", "languageVersion": "2.15"}, {"name": "image_picker", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-1.1.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "image_picker_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "image_picker_for_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_ios", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_macos", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "injectable", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/injectable-2.5.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "injectable_generator", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/injectable_generator-2.7.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "injector", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/injector-4.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "integration_test", "rootUri": "file:///Users/<USER>/Development/flutter/packages/integration_test", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "internet_connection_checker_plus", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/internet_connection_checker_plus-2.7.2", "packageUri": "lib/", "languageVersion": "2.15"}, {"name": "intl", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "io", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/io-1.0.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "isolate_channel", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/isolate_channel-0.2.2+1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "js", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/js-0.7.2", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "json_annotation", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "leak_tracker", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.8", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_flutter_testing", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_testing", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "lints", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/lints-4.0.0", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "logging", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "lottie", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.0", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "matcher", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "material_color_utilities", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "meta", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "mime", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "mocktail", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/mocktail-1.0.4", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "nested", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "nm", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/nm-0.5.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "node_preamble", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/node_preamble-2.0.2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "notifications", "rootUri": "../../notifications", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "octo_image", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "os_detect", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/os_detect-2.0.3", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "package_config", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_config-2.2.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "package_info_plus", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "package_info_plus_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus_platform_interface-3.2.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "path", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "path_parsing", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_parsing-1.1.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "path_provider", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "path_provider_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "path_provider_foundation", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "path_provider_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "path_provider_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "path_provider_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "patrol", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/patrol-3.14.1", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "patrol_finders", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/patrol_finders-2.7.2", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "patrol_log", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/patrol_log-0.3.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "petitparser", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "platform", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "plugin_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "pool", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/pool-1.5.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "process", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/process-5.0.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "provider", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "pub_semver", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/pub_semver-2.2.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "pubspec_parse", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/pubspec_parse-1.5.0", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "recase", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/recase-4.1.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "rive", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/rive-0.13.20", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "rive_common", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/rive_common-0.4.15", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "rxdart", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.27.7", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "share_plus", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-10.1.4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "share_plus_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/share_plus_platform_interface-5.0.2", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "shared_preferences", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "shared_preferences_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "shared_preferences_foundation", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shared_preferences_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "shared_preferences_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "shared_preferences_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shared_preferences_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "shelf", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf-1.4.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shelf_packages_handler", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf_packages_handler-3.0.2", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "shelf_static", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf_static-1.1.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "shelf_web_socket", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf_web_socket-3.0.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "showcaseview", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/showcaseview-3.0.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "sign_in_with_apple", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple-6.1.4", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "sign_in_with_apple_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple_platform_interface-1.1.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "sign_in_with_apple_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple_web-2.1.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "simple_gesture_detector", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/simple_gesture_detector-0.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "skeletonizer", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/skeletonizer-1.4.3", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "sky_engine", "rootUri": "file:///Users/<USER>/Development/flutter/bin/cache/pkg/sky_engine", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "smooth_page_indicator", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "source_gen", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_gen-2.0.0", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "source_map_stack_trace", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_map_stack_trace-2.1.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "source_maps", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_maps-0.10.13", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "source_span", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "sprintf", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "sqflite", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "sqflite_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "sqflite_common", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "sqflite_darwin", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "sqflite_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "stack_trace", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "stream_channel", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "stream_transform", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "string_scanner", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "sync_http", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/sync_http-0.3.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "syncfusion_flutter_core", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-25.2.7", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "syncfusion_flutter_gauges", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_gauges-25.2.7", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "synchronized", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.3.1", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "table_calendar", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/table_calendar-3.1.3", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "term_glyph", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "test", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/test-1.25.15", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "test_api", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "test_core", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/test_core-0.6.8", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "timeago", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "timeline_tile", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/timeline_tile-2.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "timezone", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/timezone-0.10.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "timing", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/timing-1.0.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "typed_data", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "upgrader", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/upgrader-11.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "url_launcher", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "url_launcher_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "url_launcher_ios", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "url_launcher_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "url_launcher_macos", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "url_launcher_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "url_launcher_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "url_launcher_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "uuid", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "vector_graphics", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics-1.1.19", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "vector_graphics_codec", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_codec-1.1.13", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "vector_graphics_compiler", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.17", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "vector_math", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "version", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/version-3.0.2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "video_player", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player-2.10.0", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "video_player_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_android-2.8.7", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "video_player_avfoundation", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_avfoundation-2.7.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "video_player_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_platform_interface-6.3.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "video_player_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_web-2.3.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "vm_service", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-14.3.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "wakelock_plus", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.3.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "wakelock_plus_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus_platform_interface-1.2.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "watcher", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/watcher-1.1.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "web_socket", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/web_socket-1.0.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "web_socket_channel", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "webdriver", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/webdriver-3.0.4", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "webkit_inspection_protocol", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/webkit_inspection_protocol-1.2.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "win32", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "win32_registry", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-2.1.0", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "xdg_directories", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "xml", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "yaml", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "pravah", "rootUri": "../", "packageUri": "lib/", "languageVersion": "3.0"}], "generated": "2025-08-14T20:43:27.430167Z", "generator": "pub", "generatorVersion": "3.7.2", "flutterRoot": "file:///Users/<USER>/Development/flutter", "flutterVersion": "3.29.2", "pubCache": "file:///Users/<USER>/.pub-cache"}