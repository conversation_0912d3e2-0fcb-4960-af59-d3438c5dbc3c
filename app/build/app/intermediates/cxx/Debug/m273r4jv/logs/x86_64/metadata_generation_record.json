[{"level_": 0, "message_": "Start JSON generation. Platform version: 24 min SDK version: x86_64", "file_": "/Users/<USER>/Development/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt", "tag_": "prodProfile|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON '/Users/<USER>/Documents/Projects/Pravah/app/android/app/.cxx/Debug/m273r4jv/x86_64/android_gradle_build.json' was up-to-date", "file_": "/Users/<USER>/Development/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt", "tag_": "prodProfile|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "/Users/<USER>/Development/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt", "tag_": "prodProfile|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]