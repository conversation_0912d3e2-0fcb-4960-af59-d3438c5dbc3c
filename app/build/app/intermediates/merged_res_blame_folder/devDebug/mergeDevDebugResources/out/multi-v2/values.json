{"logs": [{"outputFile": "com.synamic.pravah.app-mergeDevDebugResources-55:/values/values.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/054d256a178b6160d0aafb78bfb66ef9/transformed/jetified-core-1.5.0/res/values/values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "2056,2063", "startColumns": "4,4", "startOffsets": "133993,134365", "endLines": "2062,2069", "endColumns": "8,8", "endOffsets": "134360,134730"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/fb654f2d457acfca0877367555ab221f/transformed/jetified-window-1.2.0/res/values/values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,5,11,19,30,42,48,54,55,56,57,58,320", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,280,453,672,1045,1359,1547,1734,1787,1847,1899,1944,19515", "endLines": "2,10,18,26,41,47,53,54,55,56,57,58,320", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59", "endOffsets": "159,448,667,886,1354,1542,1729,1782,1842,1894,1939,1978,19570"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/dc0cc3749c0c573f35b79d156b32bfed/transformed/media-1.1.0/res/values/values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "123,129,135,269,270,271,272,368,1986,1988,1989,1994,1996", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6434,6892,7295,16522,16575,16628,16681,21994,128951,129127,129249,129511,129706", "endColumns": "88,70,72,52,52,52,52,59,65,121,60,65,66", "endOffsets": "6518,6958,7363,16570,16623,16676,16729,22049,129012,129244,129305,129572,129768"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/03d13ce1f73bac4c97d9ff47acf808cf/transformed/recyclerview-1.0.0/res/values/values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "234,235,236,244,245,246,325", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14114,14173,14221,14888,14963,15039,19753", "endColumns": "58,47,55,74,75,71,65", "endOffsets": "14168,14216,14272,14958,15034,15106,19814"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/4c8ee95016803c567dede28321c39322/transformed/lifecycle-viewmodel-2.7.0/res/values/values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "364", "startColumns": "4", "startOffsets": "21754", "endColumns": "49", "endOffsets": "21799"}}, {"source": "/Users/<USER>/Documents/Projects/Pravah/app/build/app/generated/res/resValues/dev/debug/values/gradleResValues.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "404", "startColumns": "4", "startOffsets": "24490", "endColumns": "70", "endOffsets": "24556"}}, {"source": "/Users/<USER>/Documents/Projects/Pravah/app/android/app/src/main/res/values/styles.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "1552,1556", "startColumns": "4,4", "startOffsets": "99508,99689", "endLines": "1555,1558", "endColumns": "12,12", "endOffsets": "99684,99853"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/9d417d18a3c8b9c8a6fd18cbfb47c73e/transformed/transition-1.4.1/res/values/values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "322,323,328,335,336,355,356,357,358,359", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "19632,19672,19889,20227,20282,21299,21353,21405,21454,21515", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "19667,19714,19927,20277,20324,21348,21400,21449,21510,21560"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f6a1e60d9f77dcb3bf7fcc8ae4cdea8f/transformed/jetified-play-services-basement-18.5.0/res/values/values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "370,420", "startColumns": "4,4", "startOffsets": "22111,26112", "endColumns": "67,166", "endOffsets": "22174,26274"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e177ff8d14b4e208df21e7e5d61b5060/transformed/appcompat-1.1.0/res/values/values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "4,27,28,59,60,61,62,64,65,66,67,68,69,72,73,74,75,76,77,78,79,80,81,86,87,98,99,100,101,102,103,104,105,106,107,109,110,111,112,113,114,115,116,117,118,119,120,125,126,127,128,130,131,132,133,134,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,232,233,237,238,239,240,241,242,243,273,274,275,276,277,278,279,280,316,317,318,319,324,332,333,338,360,366,367,369,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,458,463,464,465,466,467,468,476,477,481,485,489,494,500,507,511,515,520,524,528,532,536,540,544,550,554,560,564,570,574,579,583,586,590,596,600,606,610,616,619,623,627,631,635,639,640,641,642,645,648,651,654,658,659,660,661,662,665,667,669,671,676,677,681,687,691,692,694,705,706,710,716,720,721,722,726,753,757,758,762,790,960,986,1157,1183,1214,1222,1228,1242,1264,1269,1274,1284,1293,1302,1306,1313,1321,1328,1329,1338,1341,1344,1348,1352,1356,1359,1360,1365,1370,1380,1385,1392,1398,1399,1402,1406,1411,1413,1415,1418,1421,1423,1427,1430,1437,1440,1443,1447,1449,1453,1455,1457,1459,1463,1471,1479,1491,1497,1506,1509,1520,1523,1524,1529,1530,1559,1628,1698,1699,1709,1718,1870,1872,1876,1879,1882,1885,1888,1891,1894,1897,1901,1904,1907,1910,1914,1917,1921,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1935,1936,1937,1938,1939,1940,1941,1942,1943,1944,1945,1947,1949,1950,1951,1952,1953,1954,1955,1956,1958,1959,1961,1962,1964,1966,1967,1969,1970,1971,1972,1973,1974,1976,1977,1978,1979,1980,1997,1999,2001,2003,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2014,2015,2017,2018,2019,2020,2021,2022,2024,2028,2044,2045,2046,2047,2048,2049,2053,2054,2055,2070,2072,2074,2076,2078,2080,2081,2082,2083,2085,2087,2089,2090,2091,2092,2093,2094,2095,2096,2097,2098,2099,2100,2103,2104,2105,2106,2108,2110,2111,2113,2114,2116,2118,2120,2121,2122,2123,2124,2125,2126,2127,2128,2129,2130,2131,2133,2134,2135,2136,2138,2139,2140,2141,2142,2144,2146,2148,2150,2151,2152,2153,2154,2155,2156,2157,2158,2159,2160,2161,2162,2163,2164", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "225,891,936,1983,2024,2079,2138,2273,2354,2415,2490,2566,2643,2881,2966,3048,3124,3200,3277,3355,3461,3567,3646,3975,4032,4892,4966,5041,5106,5172,5232,5293,5365,5438,5505,5630,5689,5748,5807,5866,5925,5979,6033,6086,6140,6194,6248,6592,6666,6745,6818,6963,7035,7107,7180,7237,7368,7442,7516,7591,7663,7736,7806,7877,7937,7998,8067,8136,8206,8280,8356,8420,8497,8573,8650,8715,8784,8861,8936,9005,9073,9150,9216,9277,9374,9439,9508,9607,9678,9737,9795,9852,9911,9975,10046,10118,10190,10262,10334,10401,10469,10537,10596,10659,10723,10813,10904,10964,11030,11097,11163,11233,11297,11350,11417,11478,11545,11658,11716,11779,11844,11909,11984,12057,12129,12178,12239,12300,12361,12423,12487,12551,12615,12680,12743,12803,12864,12930,12989,13049,13111,13182,13242,13941,14027,14277,14367,14454,14542,14624,14707,14797,16734,16786,16844,16889,16955,17019,17076,17133,19310,19367,19415,19464,19719,20089,20136,20394,21565,21868,21932,22054,22375,22449,22519,22597,22651,22721,22806,22854,22900,22961,23024,23090,23154,23225,23288,23353,23417,23478,23539,23591,23664,23738,23807,23882,23956,24030,24171,29551,29912,29990,30080,30168,30264,30354,30936,31025,31272,31553,31805,32090,32483,32960,33182,33404,33680,33907,34137,34367,34597,34827,35054,35473,35699,36124,36354,36782,37001,37284,37492,37623,37850,38276,38501,38928,39149,39574,39694,39970,40271,40595,40886,41200,41337,41468,41573,41815,41982,42186,42394,42665,42777,42889,42994,43111,43325,43471,43611,43697,44045,44133,44379,44797,45046,45128,45226,45818,45918,46170,46594,46849,46943,47032,47269,49293,49535,49637,49890,52046,62578,64094,74725,76253,78010,78636,79056,80117,81382,81638,81874,82421,82915,83520,83718,84298,84862,85237,85355,85893,86050,86246,86519,86775,86945,87086,87150,87515,87882,88558,88822,89160,89513,89607,89793,90099,90361,90486,90613,90852,91063,91182,91375,91552,92007,92188,92310,92569,92682,92869,92971,93078,93207,93482,93990,94486,95363,95657,96227,96376,97108,97280,97364,97700,97792,99858,105104,110493,110555,111133,111717,119664,119777,120006,120166,120318,120489,120655,120824,120991,121154,121397,121567,121740,121911,122185,122384,122589,122919,123003,123099,123195,123293,123393,123495,123597,123699,123801,123903,124003,124099,124211,124340,124463,124594,124725,124823,124937,125031,125171,125305,125401,125513,125613,125729,125825,125937,126037,126177,126313,126477,126607,126765,126915,127056,127200,127335,127447,127597,127725,127853,127989,128121,128251,128381,128493,129773,129919,130063,130201,130267,130357,130433,130537,130627,130729,130837,130945,131045,131125,131217,131315,131425,131503,131609,131701,131805,131915,132037,132200,132990,133070,133170,133260,133370,133460,133701,133795,133901,134735,134835,134947,135061,135177,135293,135387,135501,135613,135715,135835,135957,136039,136143,136263,136389,136487,136581,136669,136781,136897,137019,137131,137306,137422,137508,137600,137712,137836,137903,138029,138097,138225,138369,138497,138566,138661,138776,138889,138988,139097,139208,139319,139420,139525,139625,139755,139846,139969,140063,140175,140261,140365,140461,140549,140667,140771,140875,141001,141089,141197,141297,141387,141497,141581,141683,141767,141821,141885,141991,142077,142187,142271", "endLines": "4,27,28,59,60,61,62,64,65,66,67,68,69,72,73,74,75,76,77,78,79,80,81,86,87,98,99,100,101,102,103,104,105,106,107,109,110,111,112,113,114,115,116,117,118,119,120,125,126,127,128,130,131,132,133,134,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,232,233,237,238,239,240,241,242,243,273,274,275,276,277,278,279,280,316,317,318,319,324,332,333,338,360,366,367,369,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,458,463,464,465,466,467,475,476,480,484,488,493,499,506,510,514,519,523,527,531,535,539,543,549,553,559,563,569,573,578,582,585,589,595,599,605,609,615,618,622,626,630,634,638,639,640,641,644,647,650,653,657,658,659,660,661,664,666,668,670,675,676,680,686,690,691,693,704,705,709,715,719,720,721,725,752,756,757,761,789,959,985,1156,1182,1213,1221,1227,1241,1263,1268,1273,1283,1292,1301,1305,1312,1320,1327,1328,1337,1340,1343,1347,1351,1355,1358,1359,1364,1369,1379,1384,1391,1397,1398,1401,1405,1410,1412,1414,1417,1420,1422,1426,1429,1436,1439,1442,1446,1448,1452,1454,1456,1458,1462,1470,1478,1490,1496,1505,1508,1519,1522,1523,1528,1529,1534,1627,1697,1698,1708,1717,1718,1871,1875,1878,1881,1884,1887,1890,1893,1896,1900,1903,1906,1909,1913,1916,1920,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1935,1936,1937,1938,1939,1940,1941,1942,1943,1944,1946,1948,1949,1950,1951,1952,1953,1954,1955,1957,1958,1960,1961,1963,1965,1966,1968,1969,1970,1971,1972,1973,1975,1976,1977,1978,1979,1980,1998,2000,2002,2003,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2014,2016,2017,2018,2019,2020,2021,2023,2027,2031,2044,2045,2046,2047,2048,2052,2053,2054,2055,2071,2073,2075,2077,2079,2080,2081,2082,2084,2086,2088,2089,2090,2091,2092,2093,2094,2095,2096,2097,2098,2099,2102,2103,2104,2105,2107,2109,2110,2112,2113,2115,2117,2119,2120,2121,2122,2123,2124,2125,2126,2127,2128,2129,2130,2132,2133,2134,2135,2137,2138,2139,2140,2141,2143,2145,2147,2149,2150,2151,2152,2153,2154,2155,2156,2157,2158,2159,2160,2161,2162,2163,2164", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,71,71,72,56,57,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119", "endOffsets": "275,931,980,2019,2074,2133,2195,2349,2410,2485,2561,2638,2716,2961,3043,3119,3195,3272,3350,3456,3562,3641,3721,4027,4085,4961,5036,5101,5167,5227,5288,5360,5433,5500,5568,5684,5743,5802,5861,5920,5974,6028,6081,6135,6189,6243,6297,6661,6740,6813,6887,7030,7102,7175,7232,7290,7437,7511,7586,7658,7731,7801,7872,7932,7993,8062,8131,8201,8275,8351,8415,8492,8568,8645,8710,8779,8856,8931,9000,9068,9145,9211,9272,9369,9434,9503,9602,9673,9732,9790,9847,9906,9970,10041,10113,10185,10257,10329,10396,10464,10532,10591,10654,10718,10808,10899,10959,11025,11092,11158,11228,11292,11345,11412,11473,11540,11653,11711,11774,11839,11904,11979,12052,12124,12173,12234,12295,12356,12418,12482,12546,12610,12675,12738,12798,12859,12925,12984,13044,13106,13177,13237,13305,14022,14109,14362,14449,14537,14619,14702,14792,14883,16781,16839,16884,16950,17014,17071,17128,17182,19362,19410,19459,19510,19748,20131,20180,20435,21592,21927,21989,22106,22444,22514,22592,22646,22716,22801,22849,22895,22956,23019,23085,23149,23220,23283,23348,23412,23473,23534,23586,23659,23733,23802,23877,23951,24025,24166,24236,29599,29985,30075,30163,30259,30349,30931,31020,31267,31548,31800,32085,32478,32955,33177,33399,33675,33902,34132,34362,34592,34822,35049,35468,35694,36119,36349,36777,36996,37279,37487,37618,37845,38271,38496,38923,39144,39569,39689,39965,40266,40590,40881,41195,41332,41463,41568,41810,41977,42181,42389,42660,42772,42884,42989,43106,43320,43466,43606,43692,44040,44128,44374,44792,45041,45123,45221,45813,45913,46165,46589,46844,46938,47027,47264,49288,49530,49632,49885,52041,62573,64089,74720,76248,78005,78631,79051,80112,81377,81633,81869,82416,82910,83515,83713,84293,84857,85232,85350,85888,86045,86241,86514,86770,86940,87081,87145,87510,87877,88553,88817,89155,89508,89602,89788,90094,90356,90481,90608,90847,91058,91177,91370,91547,92002,92183,92305,92564,92677,92864,92966,93073,93202,93477,93985,94481,95358,95652,96222,96371,97103,97275,97359,97695,97787,98065,105099,110488,110550,111128,111712,111803,119772,120001,120161,120313,120484,120650,120819,120986,121149,121392,121562,121735,121906,122180,122379,122584,122914,122998,123094,123190,123288,123388,123490,123592,123694,123796,123898,123998,124094,124206,124335,124458,124589,124720,124818,124932,125026,125166,125300,125396,125508,125608,125724,125820,125932,126032,126172,126308,126472,126602,126760,126910,127051,127195,127330,127442,127592,127720,127848,127984,128116,128246,128376,128488,128628,129914,130058,130196,130262,130352,130428,130532,130622,130724,130832,130940,131040,131120,131212,131310,131420,131498,131604,131696,131800,131910,132032,132195,132352,133065,133165,133255,133365,133455,133696,133790,133896,133988,134830,134942,135056,135172,135288,135382,135496,135608,135710,135830,135952,136034,136138,136258,136384,136482,136576,136664,136776,136892,137014,137126,137301,137417,137503,137595,137707,137831,137898,138024,138092,138220,138364,138492,138561,138656,138771,138884,138983,139092,139203,139314,139415,139520,139620,139750,139841,139964,140058,140170,140256,140360,140456,140544,140662,140766,140870,140996,141084,141192,141292,141382,141492,141576,141678,141762,141816,141880,141986,142072,142182,142266,142386"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/261532a48edc370e4058463405e7400b/transformed/browser-1.8.0/res/values/values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "82,83,84,85,223,224,431,443,444,445", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "3726,3784,3850,3913,13310,13381,27443,28358,28425,28504", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "3779,3845,3908,3970,13376,13448,27506,28420,28499,28568"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/b5ba2f2efa673e7de321d6c4f861e7e1/transformed/jetified-play-services-base-18.5.0/res/values/values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "90,91,92,93,94,95,96,97,412,413,414,415,416,417,418,419,421,422,423,424,425,426,427,428,429", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4221,4311,4391,4481,4571,4651,4732,4812,25072,25177,25358,25483,25590,25770,25893,26009,26279,26467,26572,26753,26878,27053,27201,27264,27326", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78", "endOffsets": "4306,4386,4476,4566,4646,4727,4807,4887,25172,25353,25478,25585,25765,25888,26004,26107,26462,26567,26748,26873,27048,27196,27259,27321,27400"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/c68ff7d8d5a0f6fbc54821041e868231/transformed/jetified-startup-runtime-1.1.1/res/values/values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "403", "startColumns": "4", "startOffsets": "24407", "endColumns": "82", "endOffsets": "24485"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/413da5de2c17b597634838463cac5e10/transformed/lifecycle-runtime-2.7.0/res/values/values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "361", "startColumns": "4", "startOffsets": "21597", "endColumns": "42", "endOffsets": "21635"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/b5419515f28c9aa820a690a25a760a1d/transformed/core-1.13.1/res/values/values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "29,70,71,88,89,121,122,225,226,227,228,229,230,231,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,326,327,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,373,405,406,407,408,409,410,411,459,1981,1982,1987,1990,1995,2165,2166", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "985,2721,2793,4090,4155,6302,6371,13453,13523,13591,13663,13733,13794,13868,15111,15172,15233,15295,15359,15421,15482,15550,15650,15710,15776,15849,15918,15975,16027,17187,17259,17335,17400,17459,17518,17578,17638,17698,17758,17818,17878,17938,17998,18058,18118,18177,18237,18297,18357,18417,18477,18537,18597,18657,18717,18777,18836,18896,18956,19015,19074,19133,19192,19251,19819,19854,20440,20495,20558,20613,20671,20729,20790,20853,20910,20961,21011,21072,21129,21195,21229,21264,22305,24561,24628,24700,24769,24838,24912,24984,29604,128633,128750,129017,129310,129577,142391,142463", "endLines": "29,70,71,88,89,121,122,225,226,227,228,229,230,231,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,326,327,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,373,405,406,407,408,409,410,411,459,1981,1985,1987,1993,1995,2165,2166", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66", "endOffsets": "1040,2788,2876,4150,4216,6366,6429,13518,13586,13658,13728,13789,13863,13936,15167,15228,15290,15354,15416,15477,15545,15645,15705,15771,15844,15913,15970,16022,16084,17254,17330,17395,17454,17513,17573,17633,17693,17753,17813,17873,17933,17993,18053,18113,18172,18232,18292,18352,18412,18472,18532,18592,18652,18712,18772,18831,18891,18951,19010,19069,19128,19187,19246,19305,19849,19884,20490,20553,20608,20666,20724,20785,20848,20905,20956,21006,21067,21124,21190,21224,21259,21294,22370,24623,24695,24764,24833,24907,24979,25067,29670,128745,128946,129122,129506,129701,142458,142525"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bf7b1ec85df6ee9a851e075600b9e9e4/transformed/fragment-1.7.1/res/values/values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "321,337,365", "startColumns": "4,4,4", "startOffsets": "19575,20329,21804", "endColumns": "56,64,63", "endOffsets": "19627,20389,21863"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/62dc587bff7657fc131ba7856fb0c52f/transformed/coordinatorlayout-1.0.0/res/values/values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "3,2167", "startColumns": "4,4", "startOffsets": "164,142530", "endLines": "3,2169", "endColumns": "60,12", "endOffsets": "220,142670"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/939a1c80bb0a2abe3b2700992a6a3ede/transformed/jetified-credentials-1.2.0-rc01/res/values/values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "401,402", "startColumns": "4,4", "startOffsets": "24241,24323", "endColumns": "81,83", "endOffsets": "24318,24402"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a61b6527ccc4c1354952455ec7d0ab2d/transformed/jetified-credentials-play-services-auth-1.2.0-rc01/res/values/values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2032", "startColumns": "4", "startOffsets": "132357", "endLines": "2035", "endColumns": "12", "endOffsets": "132575"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bbbabbd99cde3475bfc3deb14fd0af5d/transformed/jetified-activity-1.9.3/res/values/values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "334,362", "startColumns": "4,4", "startOffsets": "20185,21640", "endColumns": "41,59", "endOffsets": "20222,21695"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/894faaa4b49630aba93d6a2da0624de9/transformed/jetified-savedstate-1.2.1/res/values/values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "363", "startColumns": "4", "startOffsets": "21700", "endColumns": "53", "endOffsets": "21749"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/b578990f8c72b69970c09ca3a5da2257/transformed/jetified-media3-exoplayer-1.4.1/res/values/values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "433,434,435,436,437,438,439,440,441", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "27655,27725,27787,27852,27916,27993,28058,28148,28232", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "27720,27782,27847,27911,27988,28053,28143,28227,28296"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/0afe223afc4e515942b72a8bb230120f/transformed/preference-1.2.1/res/values/values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "63,124,262,263,264,265,266,267,268,329,330,331,371,372,430,442,453,454,460,461,462,1535,1719,1722,1728,1734,1737,1743,1747,1750,1757,1763,1766,1772,1777,1782,1789,1791,1797,1803,1811,1816,1823,1828,1834,1838,1845,1849,1855,1861,1864,1868,1869", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2200,6523,16089,16153,16208,16276,16343,16408,16465,19932,19980,20028,22179,22242,27405,28301,29294,29338,29675,29814,29864,98070,111808,111913,112158,112496,112642,112982,113194,113357,113764,114102,114225,114564,114803,115060,115431,115491,115829,116115,116564,116856,117244,117549,117893,118138,118468,118675,118943,119216,119360,119561,119608", "endLines": "63,124,262,263,264,265,266,267,268,329,330,331,371,372,430,442,453,456,460,461,462,1551,1721,1727,1733,1736,1742,1746,1749,1756,1762,1765,1771,1776,1781,1788,1790,1796,1802,1810,1815,1822,1827,1833,1837,1844,1848,1854,1860,1863,1867,1868,1869", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55", "endOffsets": "2268,6587,16148,16203,16271,16338,16403,16460,16517,19975,20023,20084,22237,22300,27438,28353,29333,29473,29809,29859,29907,99503,111908,112153,112491,112637,112977,113189,113352,113759,114097,114220,114559,114798,115055,115426,115486,115824,116110,116559,116851,117239,117544,117888,118133,118463,118670,118938,119211,119355,119556,119603,119659"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/d4015151dd7efe6bbebb0d20cc49b6af/transformed/jetified-firebase-messaging-24.1.1/res/values/values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "446", "startColumns": "4", "startOffsets": "28573", "endColumns": "81", "endOffsets": "28650"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e524b86c9ef551d66027a971ed5a7f6e/transformed/jetified-core-common-2.0.3/res/values/values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2036", "startColumns": "4", "startOffsets": "132580", "endLines": "2043", "endColumns": "8", "endOffsets": "132985"}}, {"source": "/Users/<USER>/Documents/Projects/Pravah/app/android/app/src/dev/res/values/values.xml", "from": {"startLines": "3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "106,250,370,452,556,665,785,889", "endColumns": "143,119,81,103,108,119,103,72", "endOffsets": "245,365,447,551,660,780,884,957"}, "to": {"startLines": "432,447,448,449,450,451,452,457", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "27511,28655,28775,28857,28961,29070,29190,29478", "endColumns": "143,119,81,103,108,119,103,72", "endOffsets": "27650,28770,28852,28956,29065,29185,29289,29546"}}, {"source": "/Users/<USER>/Documents/Projects/Pravah/app/android/app/src/main/res/values/colors.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "56", "endOffsets": "107"}, "to": {"startLines": "108", "startColumns": "4", "startOffsets": "5573", "endColumns": "56", "endOffsets": "5625"}}]}, {"outputFile": "/Users/<USER>/.gradle/daemon/8.9/com.synamic.pravah.app-mergeDevDebugResources-55:/values/values.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/054d256a178b6160d0aafb78bfb66ef9/transformed/jetified-core-1.5.0/res/values/values.xml", "from": {"startLines": "4,11", "startColumns": "0,0", "startOffsets": "176,544", "endLines": "10,17", "endColumns": "8,8", "endOffsets": "543,909"}, "to": {"startLines": "2047,2054", "startColumns": "4,4", "startOffsets": "133080,133452", "endLines": "2053,2060", "endColumns": "8,8", "endOffsets": "133447,133817"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/fb654f2d457acfca0877367555ab221f/transformed/jetified-window-1.2.0/res/values/values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "2,5,11,19,30,42,48,54,55,56,57,58,319,2265,2271,3589,3597,3612", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,280,453,672,1045,1359,1547,1734,1787,1847,1899,1944,19458,145285,145480,190635,190917,191531", "endLines": "2,10,18,26,41,47,53,54,55,56,57,58,319,2270,2275,3596,3611,3627", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "159,448,667,886,1354,1542,1729,1782,1842,1894,1939,1978,19513,145475,145633,190912,191526,192180"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/dc0cc3749c0c573f35b79d156b32bfed/transformed/media-1.1.0/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,215,288,341,394,447,500,560,626,748,809,875", "endColumns": "88,70,72,52,52,52,52,59,65,121,60,65,66", "endOffsets": "139,210,283,336,389,442,495,555,621,743,804,870,937"}, "to": {"startLines": "122,128,134,268,269,270,271,367,1977,1979,1980,1985,1987", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6377,6835,7238,16465,16518,16571,16624,21937,128038,128214,128336,128598,128793", "endColumns": "88,70,72,52,52,52,52,59,65,121,60,65,66", "endOffsets": "6461,6901,7306,16513,16566,16619,16672,21992,128099,128331,128392,128659,128855"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/03d13ce1f73bac4c97d9ff47acf808cf/transformed/recyclerview-1.0.0/res/values/values.xml", "from": {"startLines": "30,31,32,33,34,35,36,2", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1535,1594,1642,1698,1773,1849,1921,55", "endLines": "30,31,32,33,34,35,36,29", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "1589,1637,1693,1768,1844,1916,1982,1530"}, "to": {"startLines": "233,234,235,243,244,245,324,3490", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "14057,14116,14164,14831,14906,14982,19696,187576", "endLines": "233,234,235,243,244,245,324,3509", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "14111,14159,14215,14901,14977,15049,19757,188366"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/4c8ee95016803c567dede28321c39322/transformed/lifecycle-viewmodel-2.7.0/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "363", "startColumns": "4", "startOffsets": "21697", "endColumns": "49", "endOffsets": "21742"}}, {"source": "/Users/<USER>/Documents/Projects/Pravah/app/build/app/generated/res/resValues/dev/debug/values/gradleResValues.xml", "from": {"startLines": "6", "startColumns": "4", "startOffsets": "158", "endColumns": "70", "endOffsets": "224"}, "to": {"startLines": "403", "startColumns": "4", "startOffsets": "24433", "endColumns": "70", "endOffsets": "24499"}}, {"source": "/Users/<USER>/Documents/Projects/Pravah/app/android/app/src/main/res/values/styles.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "173,818", "endLines": "7,16", "endColumns": "12,12", "endOffsets": "476,982"}, "to": {"startLines": "1543,1547", "startColumns": "4,4", "startOffsets": "98595,98776", "endLines": "1546,1549", "endColumns": "12,12", "endOffsets": "98771,98940"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/9d417d18a3c8b9c8a6fd18cbfb47c73e/transformed/transition-1.4.1/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,95,142,185,240,287,341,393,442,503", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "90,137,180,235,282,336,388,437,498,548"}, "to": {"startLines": "321,322,327,334,335,354,355,356,357,358", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "19575,19615,19832,20170,20225,21242,21296,21348,21397,21458", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "19610,19657,19870,20220,20267,21291,21343,21392,21453,21503"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/f6a1e60d9f77dcb3bf7fcc8ae4cdea8f/transformed/jetified-play-services-basement-18.5.0/res/values/values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "369,419", "startColumns": "4,4", "startOffsets": "22054,26055", "endColumns": "67,166", "endOffsets": "22117,26217"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e177ff8d14b4e208df21e7e5d61b5060/transformed/appcompat-1.1.0/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,54,55,56,57,58,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,218,219,223,227,231,236,242,249,253,257,262,266,270,274,278,282,286,292,296,302,306,312,316,321,325,328,332,338,342,348,352,358,361,365,369,373,377,381,382,383,384,387,390,393,396,400,401,402,403,404,407,409,411,413,418,419,423,429,433,434,436,447,448,452,458,462,463,464,468,495,499,500,504,532,703,729,901,927,958,966,972,986,1008,1013,1018,1028,1037,1046,1050,1057,1065,1072,1073,1082,1085,1088,1092,1096,1100,1103,1104,1109,1114,1124,1129,1136,1142,1143,1146,1150,1155,1157,1159,1162,1165,1167,1171,1174,1181,1184,1187,1191,1193,1197,1199,1201,1203,1207,1215,1223,1235,1241,1250,1253,1264,1267,1268,1273,1274,1279,1348,1418,1419,1429,1438,1439,1441,1445,1448,1451,1454,1457,1460,1463,1466,1470,1473,1476,1479,1483,1486,1490,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1516,1518,1519,1520,1521,1522,1523,1524,1525,1527,1528,1530,1531,1533,1535,1536,1538,1539,1540,1541,1542,1543,1545,1546,1547,1548,1549,1550,1552,1554,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1570,1571,1572,1573,1574,1575,1577,1581,1585,1586,1587,1588,1589,1590,1594,1595,1596,1597,1599,1601,1603,1605,1607,1608,1609,1610,1612,1614,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1630,1631,1632,1633,1635,1637,1638,1640,1641,1643,1645,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1660,1661,1662,1663,1665,1666,1667,1668,1669,1671,1673,1675,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1775,1778,1781,1784,1798,1809,1819,1849,1876,1885,1960,2357,2362,2390,2408,2444,2450,2456,2479,2620,2640,2646,2650,2656,2693,2705,2771,2795,2864,2883,2909", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,409,471,552,613,688,764,841,919,1004,1086,1162,1238,1315,1393,1499,1605,1684,1764,1821,1879,1953,2028,2093,2159,2219,2280,2352,2425,2492,2560,2619,2678,2737,2796,2855,2909,2963,3016,3070,3124,3178,3232,3306,3385,3458,3603,3675,3747,3820,3877,4008,4082,4156,4231,4303,4376,4446,4517,4577,4638,4707,4776,4846,4920,4996,5060,5137,5213,5290,5355,5424,5501,5576,5645,5713,5790,5856,5917,6014,6079,6148,6247,6318,6377,6435,6492,6551,6615,6686,6758,6830,6902,6974,7041,7109,7177,7236,7299,7363,7453,7544,7604,7670,7737,7803,7873,7937,7990,8057,8118,8185,8298,8356,8419,8484,8549,8624,8697,8769,8818,8879,8940,9001,9063,9127,9191,9255,9320,9383,9443,9504,9570,9629,9689,9751,9822,9882,9950,10036,10123,10213,10300,10388,10470,10553,10643,10734,10786,10844,10889,10955,11019,11076,11133,11187,11244,11292,11341,11392,11426,11473,11522,11568,11600,11664,11786,11843,11917,11987,12065,12119,12189,12274,12322,12368,12429,12492,12558,12622,12693,12756,12821,12885,12946,13007,13059,13132,13206,13275,13350,13424,13498,13639,13709,13762,13840,13930,14018,14114,14204,14786,14875,15122,15403,15655,15940,16333,16810,17032,17254,17530,17757,17987,18217,18447,18677,18904,19323,19549,19974,20204,20632,20851,21134,21342,21473,21700,22126,22351,22778,22999,23424,23544,23820,24121,24445,24736,25050,25187,25318,25423,25665,25832,26036,26244,26515,26627,26739,26844,26961,27175,27321,27461,27547,27895,27983,28229,28647,28896,28978,29076,29693,29793,30045,30469,30724,30818,30907,31144,33196,33438,33540,33793,35977,47101,48617,59840,61368,63125,63751,64171,65232,66497,66753,66989,67536,68030,68635,68833,69413,69977,70352,70470,71008,71165,71361,71634,71890,72060,72201,72265,72630,72997,73673,73937,74275,74628,74722,74908,75214,75476,75601,75728,75967,76178,76297,76490,76667,77122,77303,77425,77684,77797,77984,78086,78193,78322,78597,79105,79601,80478,80772,81342,81491,82223,82395,82479,82815,82907,83185,88594,94146,94208,94838,95452,95543,95656,95885,96045,96197,96368,96534,96703,96870,97033,97276,97446,97619,97790,98064,98263,98468,98798,98882,98978,99074,99172,99272,99374,99476,99578,99680,99782,99882,99978,100090,100219,100342,100473,100604,100702,100816,100910,101050,101184,101280,101392,101492,101608,101704,101816,101916,102056,102192,102356,102486,102644,102794,102935,103079,103214,103326,103476,103604,103732,103868,104000,104130,104260,104372,104512,104658,104802,104940,105006,105096,105172,105276,105366,105468,105576,105684,105784,105864,105956,106054,106164,106242,106348,106440,106544,106654,106776,106939,107096,107176,107276,107366,107476,107566,107807,107901,108007,108099,108199,108311,108425,108541,108657,108751,108865,108977,109079,109199,109321,109403,109507,109627,109753,109851,109945,110033,110145,110261,110383,110495,110670,110786,110872,110964,111076,111200,111267,111393,111461,111589,111733,111861,111930,112025,112140,112253,112352,112461,112572,112683,112784,112889,112989,113119,113210,113333,113427,113539,113625,113729,113825,113913,114031,114135,114239,114365,114453,114561,114661,114751,114861,114945,115047,115131,115185,115249,115355,115441,115551,115635,115755,120899,121017,121132,121264,121979,122671,123188,124787,126320,126708,131443,151705,151965,153475,154508,156521,156783,157139,157969,164751,165885,166179,166402,166729,168779,169427,173278,174480,178559,179774,181183", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,54,55,56,57,58,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,217,218,222,226,230,235,241,248,252,256,261,265,269,273,277,281,285,291,295,301,305,311,315,320,324,327,331,337,341,347,351,357,360,364,368,372,376,380,381,382,383,386,389,392,395,399,400,401,402,403,406,408,410,412,417,418,422,428,432,433,435,446,447,451,457,461,462,463,467,494,498,499,503,531,702,728,900,926,957,965,971,985,1007,1012,1017,1027,1036,1045,1049,1056,1064,1071,1072,1081,1084,1087,1091,1095,1099,1102,1103,1108,1113,1123,1128,1135,1141,1142,1145,1149,1154,1156,1158,1161,1164,1166,1170,1173,1180,1183,1186,1190,1192,1196,1198,1200,1202,1206,1214,1222,1234,1240,1249,1252,1263,1266,1267,1272,1273,1278,1347,1417,1418,1428,1437,1438,1440,1444,1447,1450,1453,1456,1459,1462,1465,1469,1472,1475,1478,1482,1485,1489,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1515,1517,1518,1519,1520,1521,1522,1523,1524,1526,1527,1529,1530,1532,1534,1535,1537,1538,1539,1540,1541,1542,1544,1545,1546,1547,1548,1549,1551,1553,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1569,1570,1571,1572,1573,1574,1576,1580,1584,1585,1586,1587,1588,1589,1593,1594,1595,1596,1598,1600,1602,1604,1606,1607,1608,1609,1611,1613,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1629,1630,1631,1632,1634,1636,1637,1639,1640,1642,1644,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1659,1660,1661,1662,1664,1665,1666,1667,1668,1670,1672,1674,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1774,1777,1780,1783,1797,1808,1818,1848,1875,1884,1959,2356,2361,2389,2407,2443,2449,2455,2478,2619,2639,2645,2649,2655,2692,2704,2770,2794,2863,2882,2908,2917", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,71,71,72,56,57,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,404,466,547,608,683,759,836,914,999,1081,1157,1233,1310,1388,1494,1600,1679,1759,1816,1874,1948,2023,2088,2154,2214,2275,2347,2420,2487,2555,2614,2673,2732,2791,2850,2904,2958,3011,3065,3119,3173,3227,3301,3380,3453,3527,3670,3742,3815,3872,3930,4077,4151,4226,4298,4371,4441,4512,4572,4633,4702,4771,4841,4915,4991,5055,5132,5208,5285,5350,5419,5496,5571,5640,5708,5785,5851,5912,6009,6074,6143,6242,6313,6372,6430,6487,6546,6610,6681,6753,6825,6897,6969,7036,7104,7172,7231,7294,7358,7448,7539,7599,7665,7732,7798,7868,7932,7985,8052,8113,8180,8293,8351,8414,8479,8544,8619,8692,8764,8813,8874,8935,8996,9058,9122,9186,9250,9315,9378,9438,9499,9565,9624,9684,9746,9817,9877,9945,10031,10118,10208,10295,10383,10465,10548,10638,10729,10781,10839,10884,10950,11014,11071,11128,11182,11239,11287,11336,11387,11421,11468,11517,11563,11595,11659,11721,11838,11912,11982,12060,12114,12184,12269,12317,12363,12424,12487,12553,12617,12688,12751,12816,12880,12941,13002,13054,13127,13201,13270,13345,13419,13493,13634,13704,13757,13835,13925,14013,14109,14199,14781,14870,15117,15398,15650,15935,16328,16805,17027,17249,17525,17752,17982,18212,18442,18672,18899,19318,19544,19969,20199,20627,20846,21129,21337,21468,21695,22121,22346,22773,22994,23419,23539,23815,24116,24440,24731,25045,25182,25313,25418,25660,25827,26031,26239,26510,26622,26734,26839,26956,27170,27316,27456,27542,27890,27978,28224,28642,28891,28973,29071,29688,29788,30040,30464,30719,30813,30902,31139,33191,33433,33535,33788,35972,47096,48612,59835,61363,63120,63746,64166,65227,66492,66748,66984,67531,68025,68630,68828,69408,69972,70347,70465,71003,71160,71356,71629,71885,72055,72196,72260,72625,72992,73668,73932,74270,74623,74717,74903,75209,75471,75596,75723,75962,76173,76292,76485,76662,77117,77298,77420,77679,77792,77979,78081,78188,78317,78592,79100,79596,80473,80767,81337,81486,82218,82390,82474,82810,82902,83180,88589,94141,94203,94833,95447,95538,95651,95880,96040,96192,96363,96529,96698,96865,97028,97271,97441,97614,97785,98059,98258,98463,98793,98877,98973,99069,99167,99267,99369,99471,99573,99675,99777,99877,99973,100085,100214,100337,100468,100599,100697,100811,100905,101045,101179,101275,101387,101487,101603,101699,101811,101911,102051,102187,102351,102481,102639,102789,102930,103074,103209,103321,103471,103599,103727,103863,103995,104125,104255,104367,104507,104653,104797,104935,105001,105091,105167,105271,105361,105463,105571,105679,105779,105859,105951,106049,106159,106237,106343,106435,106539,106649,106771,106934,107091,107171,107271,107361,107471,107561,107802,107896,108002,108094,108194,108306,108420,108536,108652,108746,108860,108972,109074,109194,109316,109398,109502,109622,109748,109846,109940,110028,110140,110256,110378,110490,110665,110781,110867,110959,111071,111195,111262,111388,111456,111584,111728,111856,111925,112020,112135,112248,112347,112456,112567,112678,112779,112884,112984,113114,113205,113328,113422,113534,113620,113724,113820,113908,114026,114130,114234,114360,114448,114556,114656,114746,114856,114940,115042,115126,115180,115244,115350,115436,115546,115630,115750,120894,121012,121127,121259,121974,122666,123183,124782,126315,126703,131438,151700,151960,153470,154503,156516,156778,157134,157964,164746,165880,166174,166397,166724,168774,169422,173273,174475,178554,179769,181178,181652"}, "to": {"startLines": "4,27,28,59,60,61,62,64,65,66,67,68,69,72,73,74,75,76,77,78,79,80,81,86,87,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,124,125,126,127,129,130,131,132,133,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,231,232,236,237,238,239,240,241,242,272,273,274,275,276,277,278,279,315,316,317,318,323,331,332,337,359,365,366,368,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,449,454,455,456,457,458,459,467,468,472,476,480,485,491,498,502,506,511,515,519,523,527,531,535,541,545,551,555,561,565,570,574,577,581,587,591,597,601,607,610,614,618,622,626,630,631,632,633,636,639,642,645,649,650,651,652,653,656,658,660,662,667,668,672,678,682,683,685,696,697,701,707,711,712,713,717,744,748,749,753,781,951,977,1148,1174,1205,1213,1219,1233,1255,1260,1265,1275,1284,1293,1297,1304,1312,1319,1320,1329,1332,1335,1339,1343,1347,1350,1351,1356,1361,1371,1376,1383,1389,1390,1393,1397,1402,1404,1406,1409,1412,1414,1418,1421,1428,1431,1434,1438,1440,1444,1446,1448,1450,1454,1462,1470,1482,1488,1497,1500,1511,1514,1515,1520,1521,1550,1619,1689,1690,1700,1709,1861,1863,1867,1870,1873,1876,1879,1882,1885,1888,1892,1895,1898,1901,1905,1908,1912,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1935,1936,1938,1940,1941,1942,1943,1944,1945,1946,1947,1949,1950,1952,1953,1955,1957,1958,1960,1961,1962,1963,1964,1965,1967,1968,1969,1970,1971,1988,1990,1992,1994,1995,1996,1997,1998,1999,2000,2001,2002,2003,2004,2005,2006,2008,2009,2010,2011,2012,2013,2015,2019,2035,2036,2037,2038,2039,2040,2044,2045,2046,2061,2063,2065,2067,2069,2071,2072,2073,2074,2076,2078,2080,2081,2082,2083,2084,2085,2086,2087,2088,2089,2090,2091,2094,2095,2096,2097,2099,2101,2102,2104,2105,2107,2109,2111,2112,2113,2114,2115,2116,2117,2118,2119,2120,2121,2122,2124,2125,2126,2127,2129,2130,2131,2132,2133,2135,2137,2139,2141,2142,2143,2144,2145,2146,2147,2148,2149,2150,2151,2152,2153,2154,2155,2161,2236,2239,2242,2245,2259,2276,2318,2347,2374,2383,2445,2809,2840,2978,3102,3126,3132,3161,3182,3306,3334,3340,3484,3510,3577,3648,3748,3768,3823,3835,3861", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "225,891,936,1983,2024,2079,2138,2273,2354,2415,2490,2566,2643,2881,2966,3048,3124,3200,3277,3355,3461,3567,3646,3975,4032,4892,4966,5041,5106,5172,5232,5293,5365,5438,5505,5573,5632,5691,5750,5809,5868,5922,5976,6029,6083,6137,6191,6535,6609,6688,6761,6906,6978,7050,7123,7180,7311,7385,7459,7534,7606,7679,7749,7820,7880,7941,8010,8079,8149,8223,8299,8363,8440,8516,8593,8658,8727,8804,8879,8948,9016,9093,9159,9220,9317,9382,9451,9550,9621,9680,9738,9795,9854,9918,9989,10061,10133,10205,10277,10344,10412,10480,10539,10602,10666,10756,10847,10907,10973,11040,11106,11176,11240,11293,11360,11421,11488,11601,11659,11722,11787,11852,11927,12000,12072,12121,12182,12243,12304,12366,12430,12494,12558,12623,12686,12746,12807,12873,12932,12992,13054,13125,13185,13884,13970,14220,14310,14397,14485,14567,14650,14740,16677,16729,16787,16832,16898,16962,17019,17076,19253,19310,19358,19407,19662,20032,20079,20337,21508,21811,21875,21997,22318,22392,22462,22540,22594,22664,22749,22797,22843,22904,22967,23033,23097,23168,23231,23296,23360,23421,23482,23534,23607,23681,23750,23825,23899,23973,24114,28638,28999,29077,29167,29255,29351,29441,30023,30112,30359,30640,30892,31177,31570,32047,32269,32491,32767,32994,33224,33454,33684,33914,34141,34560,34786,35211,35441,35869,36088,36371,36579,36710,36937,37363,37588,38015,38236,38661,38781,39057,39358,39682,39973,40287,40424,40555,40660,40902,41069,41273,41481,41752,41864,41976,42081,42198,42412,42558,42698,42784,43132,43220,43466,43884,44133,44215,44313,44905,45005,45257,45681,45936,46030,46119,46356,48380,48622,48724,48977,51133,61665,63181,73812,75340,77097,77723,78143,79204,80469,80725,80961,81508,82002,82607,82805,83385,83949,84324,84442,84980,85137,85333,85606,85862,86032,86173,86237,86602,86969,87645,87909,88247,88600,88694,88880,89186,89448,89573,89700,89939,90150,90269,90462,90639,91094,91275,91397,91656,91769,91956,92058,92165,92294,92569,93077,93573,94450,94744,95314,95463,96195,96367,96451,96787,96879,98945,104191,109580,109642,110220,110804,118751,118864,119093,119253,119405,119576,119742,119911,120078,120241,120484,120654,120827,120998,121272,121471,121676,122006,122090,122186,122282,122380,122480,122582,122684,122786,122888,122990,123090,123186,123298,123427,123550,123681,123812,123910,124024,124118,124258,124392,124488,124600,124700,124816,124912,125024,125124,125264,125400,125564,125694,125852,126002,126143,126287,126422,126534,126684,126812,126940,127076,127208,127338,127468,127580,128860,129006,129150,129288,129354,129444,129520,129624,129714,129816,129924,130032,130132,130212,130304,130402,130512,130590,130696,130788,130892,131002,131124,131287,132077,132157,132257,132347,132457,132547,132788,132882,132988,133822,133922,134034,134148,134264,134380,134474,134588,134700,134802,134922,135044,135126,135230,135350,135476,135574,135668,135756,135868,135984,136106,136218,136393,136509,136595,136687,136799,136923,136990,137116,137184,137312,137456,137584,137653,137748,137863,137976,138075,138184,138295,138406,138507,138612,138712,138842,138933,139056,139150,139262,139348,139452,139548,139636,139754,139858,139962,140088,140176,140284,140384,140474,140584,140668,140770,140854,140908,140972,141078,141164,141274,141358,141762,144378,144496,144611,144691,145052,145638,147042,148386,149747,150135,152910,162999,164039,170852,175153,175904,176166,177013,177392,181670,182524,182753,187361,188371,190323,192723,196847,197591,199722,200062,201373", "endLines": "4,27,28,59,60,61,62,64,65,66,67,68,69,72,73,74,75,76,77,78,79,80,81,86,87,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,124,125,126,127,129,130,131,132,133,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,231,232,236,237,238,239,240,241,242,272,273,274,275,276,277,278,279,315,316,317,318,323,331,332,337,359,365,366,368,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,449,454,455,456,457,458,466,467,471,475,479,484,490,497,501,505,510,514,518,522,526,530,534,540,544,550,554,560,564,569,573,576,580,586,590,596,600,606,609,613,617,621,625,629,630,631,632,635,638,641,644,648,649,650,651,652,655,657,659,661,666,667,671,677,681,682,684,695,696,700,706,710,711,712,716,743,747,748,752,780,950,976,1147,1173,1204,1212,1218,1232,1254,1259,1264,1274,1283,1292,1296,1303,1311,1318,1319,1328,1331,1334,1338,1342,1346,1349,1350,1355,1360,1370,1375,1382,1388,1389,1392,1396,1401,1403,1405,1408,1411,1413,1417,1420,1427,1430,1433,1437,1439,1443,1445,1447,1449,1453,1461,1469,1481,1487,1496,1499,1510,1513,1514,1519,1520,1525,1618,1688,1689,1699,1708,1709,1862,1866,1869,1872,1875,1878,1881,1884,1887,1891,1894,1897,1900,1904,1907,1911,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1935,1937,1939,1940,1941,1942,1943,1944,1945,1946,1948,1949,1951,1952,1954,1956,1957,1959,1960,1961,1962,1963,1964,1966,1967,1968,1969,1970,1971,1989,1991,1993,1994,1995,1996,1997,1998,1999,2000,2001,2002,2003,2004,2005,2007,2008,2009,2010,2011,2012,2014,2018,2022,2035,2036,2037,2038,2039,2043,2044,2045,2046,2062,2064,2066,2068,2070,2071,2072,2073,2075,2077,2079,2080,2081,2082,2083,2084,2085,2086,2087,2088,2089,2090,2093,2094,2095,2096,2098,2100,2101,2103,2104,2106,2108,2110,2111,2112,2113,2114,2115,2116,2117,2118,2119,2120,2121,2123,2124,2125,2126,2128,2129,2130,2131,2132,2134,2136,2138,2140,2141,2142,2143,2144,2145,2146,2147,2148,2149,2150,2151,2152,2153,2154,2155,2235,2238,2241,2244,2258,2264,2285,2346,2373,2382,2444,2803,2812,2867,2995,3125,3131,3137,3181,3305,3325,3339,3343,3489,3544,3588,3713,3767,3822,3834,3860,3867", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,71,71,72,56,57,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "275,931,980,2019,2074,2133,2195,2349,2410,2485,2561,2638,2716,2961,3043,3119,3195,3272,3350,3456,3562,3641,3721,4027,4085,4961,5036,5101,5167,5227,5288,5360,5433,5500,5568,5627,5686,5745,5804,5863,5917,5971,6024,6078,6132,6186,6240,6604,6683,6756,6830,6973,7045,7118,7175,7233,7380,7454,7529,7601,7674,7744,7815,7875,7936,8005,8074,8144,8218,8294,8358,8435,8511,8588,8653,8722,8799,8874,8943,9011,9088,9154,9215,9312,9377,9446,9545,9616,9675,9733,9790,9849,9913,9984,10056,10128,10200,10272,10339,10407,10475,10534,10597,10661,10751,10842,10902,10968,11035,11101,11171,11235,11288,11355,11416,11483,11596,11654,11717,11782,11847,11922,11995,12067,12116,12177,12238,12299,12361,12425,12489,12553,12618,12681,12741,12802,12868,12927,12987,13049,13120,13180,13248,13965,14052,14305,14392,14480,14562,14645,14735,14826,16724,16782,16827,16893,16957,17014,17071,17125,19305,19353,19402,19453,19691,20074,20123,20378,21535,21870,21932,22049,22387,22457,22535,22589,22659,22744,22792,22838,22899,22962,23028,23092,23163,23226,23291,23355,23416,23477,23529,23602,23676,23745,23820,23894,23968,24109,24179,28686,29072,29162,29250,29346,29436,30018,30107,30354,30635,30887,31172,31565,32042,32264,32486,32762,32989,33219,33449,33679,33909,34136,34555,34781,35206,35436,35864,36083,36366,36574,36705,36932,37358,37583,38010,38231,38656,38776,39052,39353,39677,39968,40282,40419,40550,40655,40897,41064,41268,41476,41747,41859,41971,42076,42193,42407,42553,42693,42779,43127,43215,43461,43879,44128,44210,44308,44900,45000,45252,45676,45931,46025,46114,46351,48375,48617,48719,48972,51128,61660,63176,73807,75335,77092,77718,78138,79199,80464,80720,80956,81503,81997,82602,82800,83380,83944,84319,84437,84975,85132,85328,85601,85857,86027,86168,86232,86597,86964,87640,87904,88242,88595,88689,88875,89181,89443,89568,89695,89934,90145,90264,90457,90634,91089,91270,91392,91651,91764,91951,92053,92160,92289,92564,93072,93568,94445,94739,95309,95458,96190,96362,96446,96782,96874,97152,104186,109575,109637,110215,110799,110890,118859,119088,119248,119400,119571,119737,119906,120073,120236,120479,120649,120822,120993,121267,121466,121671,122001,122085,122181,122277,122375,122475,122577,122679,122781,122883,122985,123085,123181,123293,123422,123545,123676,123807,123905,124019,124113,124253,124387,124483,124595,124695,124811,124907,125019,125119,125259,125395,125559,125689,125847,125997,126138,126282,126417,126529,126679,126807,126935,127071,127203,127333,127463,127575,127715,129001,129145,129283,129349,129439,129515,129619,129709,129811,129919,130027,130127,130207,130299,130397,130507,130585,130691,130783,130887,130997,131119,131282,131439,132152,132252,132342,132452,132542,132783,132877,132983,133075,133917,134029,134143,134259,134375,134469,134583,134695,134797,134917,135039,135121,135225,135345,135471,135569,135663,135751,135863,135979,136101,136213,136388,136504,136590,136682,136794,136918,136985,137111,137179,137307,137451,137579,137648,137743,137858,137971,138070,138179,138290,138401,138502,138607,138707,138837,138928,139051,139145,139257,139343,139447,139543,139631,139749,139853,139957,140083,140171,140279,140379,140469,140579,140663,140765,140849,140903,140967,141073,141159,141269,141353,141473,144373,144491,144606,144686,145047,145280,146150,148381,149742,150130,152905,162809,163129,165391,171419,175899,176161,176361,177387,181665,182271,182748,182899,187571,189449,190630,195744,197586,199717,200057,201368,201571"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/261532a48edc370e4058463405e7400b/transformed/browser-1.8.0/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "82,83,84,85,222,223,430,441,442,443", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "3726,3784,3850,3913,13253,13324,27386,28157,28224,28303", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "3779,3845,3908,3970,13319,13391,27449,28219,28298,28367"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/b5ba2f2efa673e7de321d6c4f861e7e1/transformed/jetified-play-services-base-18.5.0/res/values/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "90,91,92,93,94,95,96,97,411,412,413,414,415,416,417,418,420,421,422,423,424,425,426,427,428,3148,3558", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4221,4311,4391,4481,4571,4651,4732,4812,25015,25120,25301,25426,25533,25713,25836,25952,26222,26410,26515,26696,26821,26996,27144,27207,27269,176698,189906", "endLines": "90,91,92,93,94,95,96,97,411,412,413,414,415,416,417,418,420,421,422,423,424,425,426,427,428,3160,3576", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "4306,4386,4476,4566,4646,4727,4807,4887,25115,25296,25421,25528,25708,25831,25947,26050,26405,26510,26691,26816,26991,27139,27202,27264,27343,177008,190318"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/c68ff7d8d5a0f6fbc54821041e868231/transformed/jetified-startup-runtime-1.1.1/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "402", "startColumns": "4", "startOffsets": "24350", "endColumns": "82", "endOffsets": "24428"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/413da5de2c17b597634838463cac5e10/transformed/lifecycle-runtime-2.7.0/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "360", "startColumns": "4", "startOffsets": "21540", "endColumns": "42", "endOffsets": "21578"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/b5419515f28c9aa820a690a25a760a1d/transformed/core-1.13.1/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "29,70,71,88,89,120,121,224,225,226,227,228,229,230,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,325,326,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,372,404,405,406,407,408,409,410,450,1972,1973,1978,1981,1986,2156,2157,2813,2830,3000,3033,3063,3096", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "985,2721,2793,4090,4155,6245,6314,13396,13466,13534,13606,13676,13737,13811,15054,15115,15176,15238,15302,15364,15425,15493,15593,15653,15719,15792,15861,15918,15970,17130,17202,17278,17343,17402,17461,17521,17581,17641,17701,17761,17821,17881,17941,18001,18061,18120,18180,18240,18300,18360,18420,18480,18540,18600,18660,18720,18779,18839,18899,18958,19017,19076,19135,19194,19762,19797,20383,20438,20501,20556,20614,20672,20733,20796,20853,20904,20954,21015,21072,21138,21172,21207,22248,24504,24571,24643,24712,24781,24855,24927,28691,127720,127837,128104,128397,128664,141478,141550,163134,163738,171573,173304,174304,174986", "endLines": "29,70,71,88,89,120,121,224,225,226,227,228,229,230,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,325,326,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,372,404,405,406,407,408,409,410,450,1972,1976,1978,1984,1986,2156,2157,2818,2839,3032,3053,3095,3101", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1040,2788,2876,4150,4216,6309,6372,13461,13529,13601,13671,13732,13806,13879,15110,15171,15233,15297,15359,15420,15488,15588,15648,15714,15787,15856,15913,15965,16027,17197,17273,17338,17397,17456,17516,17576,17636,17696,17756,17816,17876,17936,17996,18056,18115,18175,18235,18295,18355,18415,18475,18535,18595,18655,18715,18774,18834,18894,18953,19012,19071,19130,19189,19248,19792,19827,20433,20496,20551,20609,20667,20728,20791,20848,20899,20949,21010,21067,21133,21167,21202,21237,22313,24566,24638,24707,24776,24850,24922,25010,28757,127832,128033,128209,128593,128788,141545,141612,163332,164034,173299,173980,174981,175148"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bf7b1ec85df6ee9a851e075600b9e9e4/transformed/fragment-1.7.1/res/values/values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "320,336,364,3054,3059", "startColumns": "4,4,4,4,4", "startOffsets": "19518,20272,21747,173985,174155", "endLines": "320,336,364,3058,3062", "endColumns": "56,64,63,24,24", "endOffsets": "19570,20332,21806,174150,174299"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/62dc587bff7657fc131ba7856fb0c52f/transformed/coordinatorlayout-1.0.0/res/values/values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "3,2158,2868,2874", "startColumns": "4,4,4,4", "startOffsets": "164,141617,165396,165607", "endLines": "3,2160,2873,2957", "endColumns": "60,12,24,24", "endOffsets": "220,141757,165602,170118"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/939a1c80bb0a2abe3b2700992a6a3ede/transformed/jetified-credentials-1.2.0-rc01/res/values/values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,137", "endColumns": "81,83", "endOffsets": "132,216"}, "to": {"startLines": "400,401", "startColumns": "4,4", "startOffsets": "24184,24266", "endColumns": "81,83", "endOffsets": "24261,24345"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/a61b6527ccc4c1354952455ec7d0ab2d/transformed/jetified-credentials-play-services-auth-1.2.0-rc01/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "12", "endOffsets": "273"}, "to": {"startLines": "2023", "startColumns": "4", "startOffsets": "131444", "endLines": "2026", "endColumns": "12", "endOffsets": "131662"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/bbbabbd99cde3475bfc3deb14fd0af5d/transformed/jetified-activity-1.9.3/res/values/values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "333,361", "startColumns": "4,4", "startOffsets": "20128,21583", "endColumns": "41,59", "endOffsets": "20165,21638"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/894faaa4b49630aba93d6a2da0624de9/transformed/jetified-savedstate-1.2.1/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "362", "startColumns": "4", "startOffsets": "21643", "endColumns": "53", "endOffsets": "21692"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/c77cfc21b4c602f80f9f19f14ca6f737/transformed/jetified-appcompat-resources-1.1.0/res/values/values.xml", "from": {"startLines": "2,29,36,47,74", "startColumns": "4,4,4,4,4", "startOffsets": "55,1702,2087,2684,4317", "endLines": "28,35,46,73,78", "endColumns": "24,24,24,24,24", "endOffsets": "1697,2082,2679,4312,4582"}, "to": {"startLines": "2286,2302,2308,3628,3644", "startColumns": "4,4,4,4,4", "startOffsets": "146155,146580,146758,192185,192596", "endLines": "2301,2307,2317,3643,3647", "endColumns": "24,24,24,24,24", "endOffsets": "146575,146753,147037,192591,192718"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/b578990f8c72b69970c09ca3a5da2257/transformed/jetified-media3-exoplayer-1.4.1/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,632", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "120,182,247,311,388,453,543,627,696"}, "to": {"startLines": "431,432,433,434,435,436,437,438,439", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "27454,27524,27586,27651,27715,27792,27857,27947,28031", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "27519,27581,27646,27710,27787,27852,27942,28026,28095"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/0afe223afc4e515942b72a8bb230120f/transformed/preference-1.2.1/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,22,23,24,25,42,45,51,57,60,66,70,73,80,86,89,95,100,105,112,114,120,126,134,139,146,151,157,161,168,172,178,184,187,192,193,194,199,215,238,243,257,268,348,358,368,386,392,439,461,485", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,178,247,311,366,434,501,566,623,680,728,776,837,900,963,1001,1058,1102,1242,1381,1431,1479,2917,3022,3378,3716,3862,4202,4414,4577,4984,5322,5445,5784,6023,6280,6651,6711,7049,7335,7784,8076,8464,8769,9113,9358,9688,9895,10163,10436,10580,10949,10996,11052,11308,12367,13788,14126,15012,15622,20168,20687,21229,22503,22763,25467,26989,28470", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,21,22,23,24,41,44,50,56,59,65,69,72,79,85,88,94,99,104,111,113,119,125,133,138,145,150,156,160,167,171,177,183,186,191,192,193,198,214,237,242,256,267,347,357,367,385,391,438,460,484,508", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "173,242,306,361,429,496,561,618,675,723,771,832,895,958,996,1053,1097,1237,1376,1426,1474,2912,3017,3373,3711,3857,4197,4409,4572,4979,5317,5440,5779,6018,6275,6646,6706,7044,7330,7779,8071,8459,8764,9108,9353,9683,9890,10158,10431,10575,10944,10991,11047,11303,12362,13783,14121,15007,15617,20163,20682,21224,22498,22758,25462,26984,28465,29984"}, "to": {"startLines": "63,123,261,262,263,264,265,266,267,328,329,330,370,371,429,440,445,446,451,452,453,1526,1710,1713,1719,1725,1728,1734,1738,1741,1748,1754,1757,1763,1768,1773,1780,1782,1788,1794,1802,1807,1814,1819,1825,1829,1836,1840,1846,1852,1855,1859,1860,2804,2819,2958,2996,3138,3326,3344,3408,3418,3428,3435,3441,3545,3714,3731", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2200,6466,16032,16096,16151,16219,16286,16351,16408,19875,19923,19971,22122,22185,27348,28100,28454,28498,28762,28901,28951,97157,110895,111000,111245,111583,111729,112069,112281,112444,112851,113189,113312,113651,113890,114147,114518,114578,114916,115202,115651,115943,116331,116636,116980,117225,117555,117762,118030,118303,118447,118648,118695,162814,163337,170123,171424,176366,182276,182904,184829,185111,185416,185678,185938,189454,195749,196279", "endLines": "63,123,261,262,263,264,265,266,267,328,329,330,370,371,429,440,445,448,451,452,453,1542,1712,1718,1724,1727,1733,1737,1740,1747,1753,1756,1762,1767,1772,1779,1781,1787,1793,1801,1806,1813,1818,1824,1828,1835,1839,1845,1851,1854,1858,1859,1860,2808,2829,2977,2999,3147,3333,3407,3417,3427,3434,3440,3483,3557,3730,3747", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "2268,6530,16091,16146,16214,16281,16346,16403,16460,19918,19966,20027,22180,22243,27381,28152,28493,28633,28896,28946,28994,98590,110995,111240,111578,111724,112064,112276,112439,112846,113184,113307,113646,113885,114142,114513,114573,114911,115197,115646,115938,116326,116631,116975,117220,117550,117757,118025,118298,118442,118643,118690,118746,162994,163733,170847,171568,176693,182519,184824,185106,185411,185673,185933,187356,189901,196274,196842"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/d4015151dd7efe6bbebb0d20cc49b6af/transformed/jetified-firebase-messaging-24.1.1/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "81", "endOffsets": "132"}, "to": {"startLines": "444", "startColumns": "4", "startOffsets": "28372", "endColumns": "81", "endOffsets": "28449"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/e524b86c9ef551d66027a971ed5a7f6e/transformed/jetified-core-common-2.0.3/res/values/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "166", "endLines": "11", "endColumns": "8", "endOffsets": "571"}, "to": {"startLines": "2027", "startColumns": "4", "startOffsets": "131667", "endLines": "2034", "endColumns": "8", "endOffsets": "132072"}}]}]}