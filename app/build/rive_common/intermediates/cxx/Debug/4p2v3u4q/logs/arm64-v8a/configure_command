/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake \
  -H/Users/<USER>/.pub-cache/hosted/pub.dev/rive_common-0.4.15/android \
  -DCMAKE_SYSTEM_NAME=Android \
  -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \
  -DCMAKE_SYSTEM_VERSION=19 \
  -DANDROID_PLATFORM=android-19 \
  -DANDROID_ABI=arm64-v8a \
  -DCMAKE_ANDROID_ARCH_ABI=arm64-v8a \
  -DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/25.1.8937393 \
  -DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/25.1.8937393 \
  -DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/25.1.8937393/build/cmake/android.toolchain.cmake \
  -DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja \
  -DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/Documents/Projects/Pravah/app/build/rive_common/intermediates/cxx/Debug/4p2v3u4q/obj/arm64-v8a \
  -DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/Documents/Projects/Pravah/app/build/rive_common/intermediates/cxx/Debug/4p2v3u4q/obj/arm64-v8a \
  -DCMAKE_BUILD_TYPE=Debug \
  -B/Users/<USER>/.pub-cache/hosted/pub.dev/rive_common-0.4.15/android/.cxx/Debug/4p2v3u4q/arm64-v8a \
  -GNinja
