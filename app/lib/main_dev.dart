import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'package:shared_preferences/shared_preferences.dart';
import 'app.dart';
import 'di.dart';
import 'helpers.dart';
import 'firebase_options.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize SharedPreferences and register it with GetIt
  final prefs = await SharedPreferences.getInstance();
  getIt.registerSingleton<SharedPreferences>(prefs);

  // Initialize Firebase first
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  tz.initializeTimeZones();

  // Register core Firebase services
  getIt.registerSingleton<FirebaseFirestore>(FirebaseFirestore.instance);
  // getIt.registerSingleton<FirebaseAuth>(FirebaseAuth.instance);
  getIt.registerSingleton<FirebaseMessaging>(FirebaseMessaging.instance);

  // Temporarily disable App Check for dev environment due to API not being enabled
  // TODO: Enable App Check after configuring it in Firebase Console
  // await FirebaseAppCheck.instance.activate(
  //     appleProvider: AppleProvider.appAttest,
  //     androidProvider: AndroidProvider.playIntegrity);
  // Configure all package dependencies in the correct order
  await configureAllPackagesDependencies(envId: 'Dev');

  debugPrint('Dependencies configured successfully');

  runApp(Pravah(env: 'Dev'));
}


// Function to handle feedback dialog


