import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:pravah/app.dart';
import 'package:firebase_core/firebase_core.dart';
import 'di.dart';
import 'helpers.dart';
import 'package:timezone/data/latest.dart' as tz;

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize SharedPreferences before any other dependencies
  final prefs = await SharedPreferences.getInstance();
  getIt.registerSingleton<SharedPreferences>(prefs);

  tz.initializeTimeZones();
  await Firebase.initializeApp();
  await FirebaseAppCheck.instance.activate(
      appleProvider: AppleProvider.appAttest,
      androidProvider: AndroidProvider.playIntegrity);

  configureAllPackagesDependencies(envId: 'Prod');
  getIt.registerSingleton<FirebaseMessaging>(FirebaseMessaging.instance);

  runApp(Pravah(env: 'Prod'));
}
