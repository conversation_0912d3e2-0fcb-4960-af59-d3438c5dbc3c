import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:pravah/app.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'di.dart';
import 'helpers.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  tz.initializeTimeZones();
  await Firebase.initializeApp();
  await FirebaseAppCheck.instance.activate(
      appleProvider: AppleProvider.appAttest,
      androidProvider: AndroidProvider.playIntegrity);
  getIt.registerSingleton<FirebaseMessaging>(FirebaseMessaging.instance);
  runApp(Pravah(env: 'UAT'));
}
