{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "cloud_firestore", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.6.8/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "cloud_functions", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_functions-5.5.1/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-5.5.4/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.13.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "fluttertoast", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_sign_in_ios", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_ios-5.8.1/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sign_in_with_apple", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple-6.1.4/", "native_build": true, "dependencies": [], "dev_dependency": false}], "android": [{"name": "cloud_firestore", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.6.8/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "cloud_functions", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_functions-5.5.1/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-5.5.4/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.13.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "fluttertoast", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_sign_in_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-6.2.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.16/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.8/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sign_in_with_apple", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple-6.1.4/", "native_build": true, "dependencies": [], "dev_dependency": false}], "macos": [{"name": "cloud_firestore", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.6.8/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "cloud_functions", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_functions-5.5.1/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-5.5.4/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.13.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_sign_in_ios", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_ios-5.8.1/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sign_in_with_apple", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple-6.1.4/", "native_build": true, "dependencies": [], "dev_dependency": false}], "linux": [{"name": "path_provider_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/", "native_build": false, "dependencies": ["path_provider_linux"], "dev_dependency": false}], "windows": [{"name": "cloud_firestore", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.6.8/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-5.5.4/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.13.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/", "native_build": false, "dependencies": ["path_provider_windows"], "dev_dependency": false}], "web": [{"name": "cloud_firestore_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_web-4.4.8/", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "cloud_functions_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_functions_web-4.11.1/", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "firebase_auth_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_web-5.14.3/", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "firebase_core_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.23.0/", "dependencies": [], "dev_dependency": false}, {"name": "fluttertoast", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/", "dependencies": [], "dev_dependency": false}, {"name": "google_sign_in_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_web-0.12.4+4/", "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/", "dependencies": [], "dev_dependency": false}, {"name": "sign_in_with_apple_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple_web-2.1.1/", "dependencies": [], "dev_dependency": false}]}, "dependencyGraph": [{"name": "cloud_firestore", "dependencies": ["cloud_firestore_web", "firebase_core"]}, {"name": "cloud_firestore_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "cloud_functions", "dependencies": ["cloud_functions_web", "firebase_core"]}, {"name": "cloud_functions_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "firebase_auth", "dependencies": ["firebase_auth_web", "firebase_core"]}, {"name": "firebase_auth_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "firebase_core", "dependencies": ["firebase_core_web"]}, {"name": "firebase_core_web", "dependencies": []}, {"name": "fluttertoast", "dependencies": []}, {"name": "google_sign_in", "dependencies": ["google_sign_in_android", "google_sign_in_ios", "google_sign_in_web"]}, {"name": "google_sign_in_android", "dependencies": []}, {"name": "google_sign_in_ios", "dependencies": []}, {"name": "google_sign_in_web", "dependencies": []}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "shared_preferences", "dependencies": ["shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_android", "dependencies": []}, {"name": "shared_preferences_foundation", "dependencies": []}, {"name": "shared_preferences_linux", "dependencies": ["path_provider_linux"]}, {"name": "shared_preferences_web", "dependencies": []}, {"name": "shared_preferences_windows", "dependencies": ["path_provider_windows"]}, {"name": "sign_in_with_apple", "dependencies": ["sign_in_with_apple_web"]}, {"name": "sign_in_with_apple_web", "dependencies": []}], "date_created": "2025-08-15 19:24:47.793388", "version": "3.29.2", "swift_package_manager_enabled": {"ios": false, "macos": false}}