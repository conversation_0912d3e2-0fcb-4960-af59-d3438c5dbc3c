// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'email_verification_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$EmailVerificationEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is EmailVerificationEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'EmailVerificationEvent()';
  }
}

/// @nodoc
class $EmailVerificationEventCopyWith<$Res> {
  $EmailVerificationEventCopyWith(
      EmailVerificationEvent _, $Res Function(EmailVerificationEvent) __);
}

/// @nodoc

class RequestVerificationEmail implements EmailVerificationEvent {
  const RequestVerificationEmail();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is RequestVerificationEmail);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'EmailVerificationEvent.requestVerificationEmail()';
  }
}

/// @nodoc

class CheckEmailVerified implements EmailVerificationEvent {
  const CheckEmailVerified();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is CheckEmailVerified);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'EmailVerificationEvent.checkEmailVerified()';
  }
}

/// @nodoc

class ResendVerificationEmail implements EmailVerificationEvent {
  const ResendVerificationEmail();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is ResendVerificationEmail);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'EmailVerificationEvent.resendVerificationEmail()';
  }
}

/// @nodoc
mixin _$EmailVerificationState {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is EmailVerificationState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'EmailVerificationState()';
  }
}

/// @nodoc
class $EmailVerificationStateCopyWith<$Res> {
  $EmailVerificationStateCopyWith(
      EmailVerificationState _, $Res Function(EmailVerificationState) __);
}

/// @nodoc

class Initial implements EmailVerificationState {
  const Initial();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is Initial);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'EmailVerificationState.initial()';
  }
}

/// @nodoc

class VerificationEmailSent implements EmailVerificationState {
  const VerificationEmailSent();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is VerificationEmailSent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'EmailVerificationState.verificationEmailSent()';
  }
}

/// @nodoc

class VerificationInProgress implements EmailVerificationState {
  const VerificationInProgress();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is VerificationInProgress);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'EmailVerificationState.verificationInProgress()';
  }
}

/// @nodoc

class EmailVerified implements EmailVerificationState {
  const EmailVerified();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is EmailVerified);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'EmailVerificationState.emailVerified()';
  }
}

/// @nodoc

class EmailNotVerified implements EmailVerificationState {
  const EmailNotVerified();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is EmailNotVerified);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'EmailVerificationState.emailNotVerified()';
  }
}

/// @nodoc

class VerificationFailed implements EmailVerificationState {
  const VerificationFailed({required this.message});

  final String message;

  /// Create a copy of EmailVerificationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $VerificationFailedCopyWith<VerificationFailed> get copyWith =>
      _$VerificationFailedCopyWithImpl<VerificationFailed>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is VerificationFailed &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  @override
  String toString() {
    return 'EmailVerificationState.verificationFailed(message: $message)';
  }
}

/// @nodoc
abstract mixin class $VerificationFailedCopyWith<$Res>
    implements $EmailVerificationStateCopyWith<$Res> {
  factory $VerificationFailedCopyWith(
          VerificationFailed value, $Res Function(VerificationFailed) _then) =
      _$VerificationFailedCopyWithImpl;
  @useResult
  $Res call({String message});
}

/// @nodoc
class _$VerificationFailedCopyWithImpl<$Res>
    implements $VerificationFailedCopyWith<$Res> {
  _$VerificationFailedCopyWithImpl(this._self, this._then);

  final VerificationFailed _self;
  final $Res Function(VerificationFailed) _then;

  /// Create a copy of EmailVerificationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? message = null,
  }) {
    return _then(VerificationFailed(
      message: null == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on
