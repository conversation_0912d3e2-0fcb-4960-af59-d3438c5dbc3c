part of 'email_verification_bloc.dart';

@freezed
abstract class EmailVerificationState with _$EmailVerificationState {
  const factory EmailVerificationState.initial() = Initial;
  const factory EmailVerificationState.verificationEmailSent() =
      VerificationEmailSent;
  const factory EmailVerificationState.verificationInProgress() =
      VerificationInProgress;
  const factory EmailVerificationState.emailVerified() = EmailVerified;
  const factory EmailVerificationState.emailNotVerified() = EmailNotVerified;
  const factory EmailVerificationState.verificationFailed(
      {required String message}) = VerificationFailed;
}
