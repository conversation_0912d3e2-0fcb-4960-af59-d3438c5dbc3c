// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:authentication/application/bloc/auth/auth_bloc.dart' as _i324;
import 'package:authentication/application/bloc/email_verification/email_verification_bloc.dart'
    as _i247;
import 'package:authentication/application/bloc/sign_in/sign_in_bloc.dart'
    as _i328;
import 'package:authentication/domain/facade/i_auth_facade.dart' as _i690;
import 'package:authentication/repository/auth_service.dart' as _i123;
import 'package:authentication/repository/firebase_injectable_module.dart'
    as _i633;
import 'package:authentication/repository/firebase_user_mapper.dart' as _i614;
import 'package:firebase_auth/firebase_auth.dart' as _i59;
import 'package:get_it/get_it.dart' as _i174;
import 'package:google_sign_in/google_sign_in.dart' as _i116;
import 'package:injectable/injectable.dart' as _i526;

extension GetItInjectableX on _i174.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  _i174.GetIt init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) {
    final gh = _i526.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    final firebaseInjectableModule = _$FirebaseInjectableModule();
    gh.lazySingleton<_i614.FirebaseUserMapper>(
        () => _i614.FirebaseUserMapper());
    gh.lazySingleton<_i116.GoogleSignIn>(
        () => firebaseInjectableModule.googleSignIn);
    gh.lazySingleton<_i59.FirebaseAuth>(
        () => firebaseInjectableModule.firebaseAuth);
    gh.lazySingleton<_i690.IAuthFacade>(() => _i123.FirebaseAuthFacade(
          gh<_i59.FirebaseAuth>(),
          gh<_i116.GoogleSignIn>(),
          gh<_i614.FirebaseUserMapper>(),
        ));
    gh.factory<_i328.SignInBloc>(
        () => _i328.SignInBloc(gh<_i690.IAuthFacade>()));
    gh.factory<_i324.AuthBloc>(() => _i324.AuthBloc(gh<_i690.IAuthFacade>()));
    gh.factory<_i247.EmailVerificationBloc>(
        () => _i247.EmailVerificationBloc(gh<_i690.IAuthFacade>()));
    return this;
  }
}

class _$FirebaseInjectableModule extends _i633.FirebaseInjectableModule {}
