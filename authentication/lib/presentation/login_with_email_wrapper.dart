// Import necessary packages
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fluttertoast/fluttertoast.dart';
import '../application/bloc/sign_in/sign_in_bloc.dart';
import '../domain/core/failures.dart';
import '../domain/failure/auth_failure.dart';
import '../domain/model/user_model.dart';
import '../widgets/text_form_feild.dart'; // Assuming you have a custom widget named MyTextFormField
/*
This file defines the EmailSignInWrapper class, a StatelessWidget that wraps the sign-in form. The form includes fields for email and password.

The class takes in a child widget, a forgotPasswordChild widget, and two callback functions, onRegisterSuccess and onRegisterFailure, which are triggered when the sign-in process succeeds or fails, respectively.

The build method of EmailSignInWrapper returns a BlocConsumer that listens to SignInBloc state changes. When the state changes, it either does nothing (if authFailureOrSuccessOption is None) or calls the appropriate callback function based on whether the sign-in was successful or not.

The GestureDetector widget at the end of the ListView triggers the SignInEvent.signInWithEmail event when tapped, which initiates the sign-in process with the entered email and password.
*/

// This class is a StatelessWidget that wraps the sign-in form.
// It takes in a child widget, a forgotPasswordChild widget, and two callback functions, onRegisterSuccess and onRegisterFailure.
class EmailSignInWrapper extends StatelessWidget {
  // Declare the child widget, the forgotPasswordChild widget, and the callback functions
  final Widget child;
  final Widget forgotPasswordChild;
  final void Function(UserModel)
      onRegisterSuccess; // Callback function for sign-in success
  final void Function(AuthFailure)
      onRegisterFailure; // Callback function for sign-in failure

  // Constructor for the EmailSignInWrapper class
  EmailSignInWrapper({
    required this.child,
    required this.forgotPasswordChild,
    required this.onRegisterSuccess,
    required this.onRegisterFailure,
  });

  // The build method returns a BlocConsumer that listens to SignInBloc state changes.
  @override
  Widget build(BuildContext context) {
    return BlocConsumer<SignInBloc, SignInState>(
      listener: (context, state) {
        // When the state changes, it either does nothing (if authFailureOrSuccessOption is None)
        // or calls the appropriate callback function based on whether the sign-in was successful or not.
        state.authFailureOrSuccessOption.map(
          (either) => either.mapBoth(
            onLeft: (failure) async {
              print(failure.toString());
              String errorMessage;
              if (failure is CancelledByUser) {
                errorMessage = 'Cancelled';
              } else if (failure is ServerError) {
                errorMessage = 'Server error';
              } else if (failure is EmailAlreadyInUse) {
                errorMessage = 'Email already in use';
              } else if (failure is InvalidEmailAndPasswordCombination) {
                errorMessage = 'Invalid email and password combination';
              } else if (failure is UserNotFound) {
                errorMessage = 'User not found';
              } else if (failure is EmailVerificationFailed) {
                errorMessage = 'Email verification failed';
              } else if (failure is EmailVerificationSendFailure) {
                errorMessage = 'Email verification email was not sent';
              } else {
                errorMessage = 'Unknown error';
              }

              Fluttertoast.showToast(
                msg: errorMessage,
                toastLength: Toast.LENGTH_LONG,
                gravity: ToastGravity.BOTTOM,
                backgroundColor: Colors.red,
                textColor: Colors.white,
              );
            },
            onRight: (success) => onRegisterSuccess(success),
          ),
        );
      },
      builder: (context, state) {
        // The builder method returns a Form widget that contains a ListView with several MyTextFormField widgets
        // for email and password input.
        // The onChanged property of these widgets dispatches events to the SignInBloc to update the state.
        return Form(
          autovalidateMode: AutovalidateMode.disabled,
          child: ListView(
            physics: NeverScrollableScrollPhysics(),
            padding: const EdgeInsets.all(8.0),
            children: <Widget>[
              // Email input field
              MyTextFormField(
                heading: 'Email',
                keyboardtype: TextInputType.emailAddress,
                isEmail: true,
                onchanged: (value) => context.read<SignInBloc>().add(
                    SignInEvent.emailChanged(
                        value ?? '')), // Handle null with a default
                validator: (_) =>
                    context.read<SignInBloc>().state.emailAddress.isValid()
                        ? null
                        : 'Invalid Email',
              ),

              const SizedBox(height: 8),
              // Password input field
              MyTextFormField(
                heading: 'Password',
                isPassword: state.obscureText,

                suffixIcon: IconButton(
                  icon: Icon(
                    state.obscureText ? Icons.visibility : Icons.visibility_off,
                  ),
                  onPressed: () {
                    context.read<SignInBloc>().add(ToggleObscureText());
                  },
                ),

                onchanged: (value) => context.read<SignInBloc>().add(
                    SignInEvent.passwordChanged(
                        value ?? '')), // Handle null with a default
                validator: (_) =>
                    context.read<SignInBloc>().state.password.isValid()
                        ? null
                        : 'password too short',
              ),

              // Forgot password widget
              forgotPasswordChild,

              const SizedBox(height: 25),

              // GestureDetector widget that triggers the SignInEvent.signInWithEmail event when tapped
              GestureDetector(
                  onTap: () {
                    FocusScope.of(context).unfocus();
                    context.read<SignInBloc>().add(SignInEvent.signInWithEmail(
                        context
                                .read<SignInBloc>()
                                .state
                                .emailAddress
                                .value
                                .getOrNull() ??
                            '', // Handle null with a default
                        context
                                .read<SignInBloc>()
                                .state
                                .password
                                .value
                                .getOrNull() ??
                            '' // Handle null with a default
                        ));
                  },
                  child: child),
            ],
          ),
        );
      },
    );
  }
}
