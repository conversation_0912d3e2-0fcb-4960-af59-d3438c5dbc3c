import 'package:firebase_auth/firebase_auth.dart'; // Importing FirebaseAuth for Firebase authentication
import 'package:google_sign_in/google_sign_in.dart'; // Importing GoogleSignIn for Google authentication
import 'package:injectable/injectable.dart'; // Importing injectable for dependency injection

// Annotation to mark the class as a module for dependency injection
@module
abstract class FirebaseInjectableModule {
  // Defining a lazy singleton for GoogleSignIn
  @lazySingleton
  GoogleSignIn get googleSignIn => GoogleSignIn(
        // Web client ID from Firebase console for server-side verification
        serverClientId:
            '503353545941-a60brdlvh968d6uen0bdjfuf9emkb0lq.apps.googleusercontent.com',
      );

  // Defining a lazy singleton for FirebaseAuth
  @lazySingleton
  FirebaseAuth get firebaseAuth => FirebaseAuth.instance;
}
