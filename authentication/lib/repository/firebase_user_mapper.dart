import 'dart:convert';

import 'package:authentication/domain/model/user_model.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';

@lazySingleton
class FirebaseUserMapper {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _firebaseAuth = FirebaseAuth.instance;

  // Method to map a User object from FirebaseAuth to a UserModel object from your domain
  // Now async to accommodate SharedPreferences and Firestore for onboarding status
  // Returns null if user document doesn't exist in Firestore (user should be logged out)
  Future<UserModel?> toDomain(User _) async {
    try {
      // Check if user document exists in Firestore
      DocumentSnapshot<Map<String, dynamic>> userDoc =
          await _firestore.collection('users').doc(_.uid).get();

      // If user document doesn't exist, create it directly in Firestore
      if (!userDoc.exists) {
        debugPrint(
            '🔄 User document not found in Firestore for uid: ${_.uid}. Creating user document directly...');

        try {
          // Create user document data directly
          final userDocData = {
            'uid': _.uid,
            'userEmail': _.email,
            'userName': _.displayName ?? (_.email?.split('@').first ?? 'User'),
            'photoURL': _.photoURL,
            'signUpTimeStamp': DateTime.now().toIso8601String(),
            'loginTimeStamp': DateTime.now().toIso8601String(),
            'isOnboarded': false, // Initialize onboarding status as false
          };

          // Create the user document in Firestore directly
          await _firestore
              .collection('users')
              .doc(_.uid)
              .set(userDocData, SetOptions(merge: true));

          debugPrint(
              '✅ User document created successfully in Firestore for uid: ${_.uid}');

          // Retry getting the user document after creation
          userDoc = await _firestore.collection('users').doc(_.uid).get();
          if (!userDoc.exists) {
            debugPrint(
                '🚫 Failed to create user document. User will be logged out.');
            await _firebaseAuth.signOut();
            return null;
          }
        } catch (e) {
          print(
              '🚫 Error creating user document directly in Firestore: $e. User will be logged out.');
          await _firebaseAuth.signOut();
          return null;
        }
      }

      // Get onboarding status from Firestore
      bool isOnboarded = false;
      if (userDoc.data()?.containsKey('isOnboarded') == true) {
        isOnboarded = userDoc.data()!['isOnboarded'] as bool? ?? false;
      } else {
        // Fallback to SharedPreferences if Firestore doesn't have the isOnboarded field
        final prefs = await SharedPreferences.getInstance();
        final String? onboardedUserIdsJson =
            prefs.getString('onboardedUserIds');
        List<String> onboardedUserIds = [];
        if (onboardedUserIdsJson != null) {
          final decoded = json.decode(onboardedUserIdsJson);
          if (decoded is List) {
            onboardedUserIds = List<String>.from(decoded);
          }
        }
        isOnboarded = onboardedUserIds.contains(_.uid);
      }

      return UserModel(
          uid: _.uid,
          userName: _.displayName ?? _.email?.split('@').first,
          userEmail: _.email,
          isOnboarded: isOnboarded,
          isEmailVerified: _.emailVerified);
    } catch (e) {
      // If Firestore fails completely, we should still log out the user
      // as we cannot verify their document exists
      print(
          '🚫 Error checking user document in Firestore for uid: ${_.uid}. Error: $e. User will be logged out.');
      // Sign out the user from Firebase Auth to ensure complete logout
      await _firebaseAuth.signOut();
      return null;
    }
  }
}
