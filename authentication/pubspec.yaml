name: authentication
description: Authentication package.
version: 0.0.1


environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.22.0 <4.0.0"


dependencies:
  flutter:
    sdk: flutter
  json_annotation:
  google_sign_in:
  sign_in_with_apple: ^6.1.3
  google_fonts: 6.1.0
  flutter_screenutil:
  firebase_auth: ^5.5.4
  cloud_firestore: ^5.5.4
  cloud_functions: ^5.2.4
  freezed_annotation: ^2.4.1
  flutter_bloc:
  injector:
  injectable:
  fpdart: ^2.0.0-dev.3
  uuid:
  bloc_test: ^9.1.7
  mocktail:
  shared_preferences:
  fluttertoast: ^8.2.12



dev_dependencies:
  melos: ^3.1.1
  build_runner:
  json_serializable:
  freezed: ^2.4.7
  injectable_generator:

  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # To add assets to your package, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/assets-and-images/#from-packages
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # To add custom fonts to your package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/custom-fonts/#from-packages
